import { Disposable } from '../../../../base/common/lifecycle.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';

export interface IChatAgentService {
	readonly _serviceBrand: undefined;

}


export const IChatAgentService = createDecorator<IChatAgentService>('chatAgentService');


export class ChatAgentService extends Disposable  implements IChatAgentService {
	readonly _serviceBrand: undefined;
	constructor() {
		super();
	}

	chatHistorySummary() {
		
	}


}
