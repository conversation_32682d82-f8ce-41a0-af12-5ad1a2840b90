/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import pWaitFor from '../../../common/pWaitFor.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { ICodeSeekExporerService } from '../common/codeseekExporerService.js';
import { ICodeseekFileService } from '../common/codeseekFileService.js';
import { ChatMode, ICodeseekSettingsService } from '../common/codeseekSettingsService.js';
import { ILLMMessageService } from '../common/llmMessageService.js';
import { SendLLMType, toLLMChatMessage, ToolCallResultCode, ToolCallResultType, ToolCallType } from '../common/llmMessageTypes.js';
import { CodebaseSelection, IMentionService, StagingSelectionItem } from '../common/selectedFileService.js';
import { ApproveRequestResultType, AskReponseType, AskResponse, InternalToolInfo, ToolCallReturnType, ToolName, ToolNameEnum } from '../common/toolsServiceTypes.js';
import { IToolsService } from '../common/toolsService.js';
import { ICodeseekTaskService } from './codeseekAgentTaskService.js';
import { ICodeseekCodeSelectionService } from './codeseekCodeSelectionService.js';
import { chat_systemMessage, chat_userMessageContent, fix_systemMessage, messageExpansion_systemMessage, user_rules } from './../common/prompt/prompts.js';
import { ISidebarStateService } from './sidebarStateService.js';
import { ICodebaseSearchService } from './codebaseSearchService.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { FeatureNames } from '../common/codeseekSettingsTypes.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { THREAD_MESSAGES_STORAGE_KEY, THREAD_ABSTRACT_STORAGE_KEY } from '../common/storageKeys.js';
import { URI } from '../../../../base/common/uri.js';
import { LINE_BREAK_REGEX } from './../common/prompt/tags.js';
import { TaskInfo } from '../../../api/browser/mainThreadCodeSeekTaskService.js';
import { IMetricsService, METRICS_EVENT } from '../common/metricsService.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { ICodeseekUacLoginService } from '../common/uac/UacloginTypes.js';
import { IUrlContentFetcherService } from '../common/IUrlContentFetchService.js';
import { AskMessage, ChatContainers, ChatMessage, ChatThreads, ContainerState, StateSelections, ThreadStreamState, ThreadType, ThreadsState, UserMessageState, userMessageOpts } from './chatThreadType.js';
import { IViewsService } from '../../../services/views/common/viewsService.js';
import { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';
import { IViewContainersRegistry, IViewsRegistry, ViewContainerLocation, Extensions as ViewExtensions } from '../../../common/views.js';
import { ViewPaneContainer } from '../../../browser/parts/views/viewPaneContainer.js';
import { Orientation } from '../../../../base/browser/ui/sash/sash.js';
import { localize2 } from '../../../../nls.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { SidebarViewPane } from './sidebarPane.js';
import { CODESEEK_NEW_CHAT_ACTION_ID, CODESEEK_VIEW_CONTAINER_ID, CODESEEK_VIEW_CONTAINER_ID_KEY } from './actionIDs.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { Codicon } from '../../../../base/common/codicons.js';

let isContainerLoaded = false;
const MAX_CONTAINERS = 3;
let order = 2;
const findLastIndex = <T>(arr: T[], condition: (t: T) => boolean): number => {
	for (let i = arr.length - 1; i >= 0; i--) {
		if (condition(arr[i])) {
			return i;
		}
	}
	return -1;
};

const defaultMessageState: UserMessageState = {
	stagingSelections: [],
	isBeingEdited: false,
};

const newThreadObject = () => {
	const now = new Date().toISOString();
	return {
		id: generateUuid(),
		createdAt: now,
		lastModified: now,
		messagesLength: 0,
		firstUserMessage: '',
		state: {
			stateSelections: { list: [], followEditorActive: true },
			focusedMessageIdx: undefined,
			isCheckedOfSelectionId: {}
		},

	} satisfies ChatThreads[string];
};


export interface IChatThreadService {
	readonly _serviceBrand: undefined;

	readonly containerState: ContainerState;
	readonly streamState: ThreadStreamState;

	onDidChangeCurrentContainer: Event<void>;
	onDidChangeCurrentThread: Event<string>;
	onDidChangeStreamState: Event<{ containerId: string, threadId: string }>;
	onDidSetChatTitle: Event<{ containerId: string, message: string }>;

	openNewContainer(): string;
	openNewThread(containerId: string): void;

	getCurrentContainerId(): string;
	getCurrentThreadId(containerId: string): string;

	isCurrentThreadWorking(containerId: string): boolean;
	getCurrentThread(containerId: string): ChatThreads[string];
	getCurrentThreadMessages(containerId: string): ChatMessage[];

	switchToContainer(containerId: string): void;
	deleteContainer(containerId: string): void;
	switchToThread(containerId: string, threadId: string, targetContainerId?: string): void;
	deleteThread(containerId: string, threadId: string): void;

	isFocusingContainer(containerId: string): boolean;

	// you can edit multiple messages
	// the one you're currently editing is "focused", and we add items to that one when you press cmd+L.
	getFocusedMessageIdx(containerId: string): number | undefined;
	isFocusingMessage(containerId: string): boolean;
	setFocusedMessageIdx(containerId: string, messageIdx: number | undefined): void;

	// exposed getters/setters
	getCurrentMessageState(containerId: string, messageIdx: number): UserMessageState;
	setCurrentMessageState(containerId: string, messageIdx: number, newState: Partial<UserMessageState>): void;
	getCurrentThreadStagingSelections(containerId: string): StagingSelectionItem[];
	setCurrentThreadStagingSelections(containerId: string, stagingSelections: StagingSelectionItem[]): void;
	getCurrentThreadStateSelections(containerId: string): StateSelections;
	setCurrentThreadStateSelectionsChangeSelections(containerId: string): void;

	// call to edit a message
	editUserMessageAndStreamResponse({ containerId, userMessage, chatMode, messageIdx }: { containerId: string; userMessage: string; chatMode: ChatMode; messageIdx: number }): Promise<void>;

	// call to add a message
	addUserMessageAndStreamResponse({ containerId, userMessageOpts, chatMode, chatSelections, agentParamsFromPlugin, taskInfo }:
		{
			containerId: string;
			userMessageOpts: userMessageOpts;
			chatMode: ChatMode;
			chatSelections?: { prevSelns?: StagingSelectionItem[]; currSelns?: StagingSelectionItem[] };
			agentParamsFromPlugin?: Record<string, any>;
			taskInfo?: TaskInfo;
		}):
		Promise<void>;

	cancelStreaming(containerId: string, threadId: string): void;
	dismissStreamError(containerId: string, threadId: string): void;
	setAskResponse(containerId: string, askResponse: AskResponse): void;
	addSelectionToChat(containerId: string, selection?: StagingSelectionItem): void;
}

export const IChatThreadService = createDecorator<IChatThreadService>('codeseekChatThreadService');
export class ChatThreadService extends Disposable implements IChatThreadService {
	_serviceBrand: undefined;

	// this fires when the current thread changes at all (a switch of currentThread, or a message added to it, etc)
	private readonly _onDidChangeCurrentThread = new Emitter<string>();
	readonly onDidChangeCurrentThread: Event<string> = this._onDidChangeCurrentThread.event;

	private readonly _onDidChangeCurrentContainer = new Emitter<void>();
	readonly onDidChangeCurrentContainer: Event<void> = this._onDidChangeCurrentContainer.event;

	private readonly _onDidSetChatTitle = new Emitter<{ containerId: string, message: string }>();
	readonly onDidSetChatTitle: Event<{ containerId: string, message: string }> = this._onDidSetChatTitle.event;

	containerState: ContainerState;
	readonly streamState: ThreadStreamState = {};

	private readonly _onDidChangeStreamState = new Emitter<{ containerId: string, threadId: string }>();
	readonly onDidChangeStreamState: Event<{ containerId: string, threadId: string }> = this._onDidChangeStreamState.event;

	constructor(
		@IStorageService private readonly _storageService: IStorageService,
		@ICodeseekFileService private readonly _codeseekFileService: ICodeseekFileService,
		@ILLMMessageService private readonly _llmMessageService: ILLMMessageService,
		@IToolsService private readonly _toolsService: IToolsService,
		@IWorkspaceContextService private readonly _workspaceContextService: IWorkspaceContextService,
		@ICodeSeekExporerService private readonly _codeSeekExporerService: ICodeSeekExporerService,
		@IModelService private readonly _modelService: IModelService,
		@ICodeseekTaskService private readonly _codeseekTaskService: ICodeseekTaskService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@ICodeseekCodeSelectionService private readonly _codeSeekCodeSelectionService: ICodeseekCodeSelectionService,
		@IEditorService private readonly _editorService: IEditorService,
		@ICodeseekSettingsService private readonly _codeseekSettingsService: ICodeseekSettingsService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@ICodebaseSearchService private readonly _codebaseSearchService: ICodebaseSearchService,
		@IMetricsService private readonly _metricsService: IMetricsService,
		@IMentionService private readonly _mentionsService: IMentionService,
		@IUrlContentFetcherService private readonly _urlContentFetcher: IUrlContentFetcherService,
		@INotificationService private readonly _notificationService: INotificationService,
		@ICodeseekUacLoginService private readonly _zteUserInfoService: ICodeseekUacLoginService,
		@IViewsService private readonly _viewsService: IViewsService,
		@ICommandService private readonly _commandService: ICommandService,
	) {
		super();

		this.containerState = {
			allContainers: {
				[CODESEEK_VIEW_CONTAINER_ID]: {
					threadsState: { allThreads: {}, currentThreadId: '', currentThreadMessages: [] }
				}
			},
			currentContainerId: CODESEEK_VIEW_CONTAINER_ID
		};

		const allContainers = this._readAllContainer();
		if (allContainers) {
			this.containerState.allContainers = allContainers;
			this._registerExistingContainers();
			// isContainerLoaded = true;
			// for (const containerId of Object.keys(this.containerState.allContainers)) {
			// 	const threadsState = this.containerState.allContainers[containerId].threadsState;
			// 	if (threadsState && threadsState.currentThreadId && threadsState.currentThreadId !== '') {
			// 		const messages = this._loadChatMessages(containerId, threadsState.currentThreadId);
			// 		threadsState.currentThreadMessages = messages;
			// 	}
			// }
		}

		if (!this.getCurrentThreadId(this.containerState.currentContainerId) || this.getCurrentThreadId(this.containerState.currentContainerId) === '') {
			this.openNewThread(this.containerState.currentContainerId);
		}

		this._editorService.onDidActiveEditorChange(() => this.onDidActiveEditorChange(this.containerState.currentContainerId));
		this._codeSeekCodeSelectionService.onDidAddContentFromEditor((selection) => this.addSelectionToChat(this.containerState.currentContainerId, selection));
		this._codeSeekCodeSelectionService.onDidAddCodeBlock((selection) => this.addSelectionToChat(this.containerState.currentContainerId, selection));

		this._viewsService.onDidChangeViewContainerVisibility(event => {
			if (event.visible && event.id.startsWith(CODESEEK_VIEW_CONTAINER_ID_KEY)) {
				this.containerState.currentContainerId = event.id;
				this._onDidChangeCurrentContainer.fire();
				this._onDidChangeCurrentThread.fire(event.id);
				if (isContainerLoaded) {
					isContainerLoaded = false;
					Object.keys(this.containerState.allContainers).forEach(containerId => {
						const container = this.containerState.allContainers[containerId];
						const messages = container.threadsState.currentThreadMessages;
						if (messages && messages.length > 0) {
							const firstUserMessage = messages.find((m: ChatMessage) => m.role === 'user');
							if (firstUserMessage && firstUserMessage.displayContent) {
								this._onDidSetChatTitle.fire({ containerId, message: firstUserMessage.displayContent });
							}
						}
					});
				}
			}
		});
	}

	public openNewContainer(): string {
		const viewContainersRegistry = Registry.as<IViewContainersRegistry>(ViewExtensions.ViewContainersRegistry);
		const viewsRegistry = Registry.as<IViewsRegistry>(ViewExtensions.ViewsRegistry);

		const allContainerIds = Object.keys(this.containerState.allContainers);

		if (allContainerIds.length >= MAX_CONTAINERS) {
			this._commandService.executeCommand(CODESEEK_NEW_CHAT_ACTION_ID);
			return this.containerState.currentContainerId;
		}

		const usedOrders = new Set<number>();
		for (const id of allContainerIds) {
			const orderStr = id.split('.')[3];
			const orderNum = parseInt(orderStr);
			if (!isNaN(orderNum)) {
				usedOrders.add(orderNum);
			}
		}

		let nextOrder = 2;
		while (usedOrders.has(nextOrder)) {
			nextOrder++;
		}

		const newContainerId = `workbench.codeseek.container.${nextOrder}.view`;
		const newViewId = newContainerId;

		const newContainer = viewContainersRegistry.registerViewContainer({
			id: newContainerId,
			title: localize2('newCodeseekContainer', 'New Chat'),
			ctorDescriptor: new SyncDescriptor(
				ViewPaneContainer, [newContainerId, {
					mergeViewWithContainerWhenSingleView: true,
					orientation: Orientation.HORIZONTAL,
				}]
			),
			hideIfEmpty: false,
			order: nextOrder,
			rejectAddedViews: true,
			icon: Codicon.symbolMethod,
		}, ViewContainerLocation.AuxiliaryBar, { doNotRegisterOpenCommand: true, isDefault: true });

		viewsRegistry.registerViews([{
			id: newViewId,
			hideByDefault: false,
			name: localize2('newCodeseekChat', 'New Chat'),
			ctorDescriptor: new SyncDescriptor(SidebarViewPane),
			canToggleVisibility: false,
			canMoveView: false,
			weight: 80,
			order: 1,
		}], newContainer);

		this.containerState.currentContainerId = newContainerId;
		this.containerState.allContainers[newContainerId] = {
			threadsState: { allThreads: {}, currentThreadId: '', currentThreadMessages: [] }
		};
		this.openNewThread(newContainerId);

		order = Math.max(order, nextOrder + 1);

		return newContainerId;
	}

	openNewThread(containerId: string) {
		const currentThread = this.getCurrentThread(containerId);
		if (currentThread && this.isCurrentThreadWorking(containerId)) {
			this.cancelStreaming(containerId, currentThread.id);
		}

		this._sidebarStateService.fireOpenNewChat(containerId);
		this._codeseekSettingsService.setChatMode(ChatMode.Ask);
		// if a thread with 0 messages already exists, switch to it
		const { allThreads } = this.containerState.allContainers[containerId].threadsState;
		for (const threadId in allThreads) {
			if (allThreads[threadId].messagesLength === 0) {
				allThreads[threadId].state.focusedMessageIdx = undefined;
				allThreads[threadId].state.stateSelections = { list: [], followEditorActive: true };
				allThreads[threadId].state.isCheckedOfSelectionId = {};
				this.switchToThread(containerId, threadId);
				this._addSelectionToChat(containerId);
				return;
			}
		}
		// otherwise, start a new thread
		const newThread = newThreadObject();

		// update state
		const newThreads: ChatThreads = {
			...allThreads,
			[newThread.id]: newThread
		};
		this._onDidSetChatTitle.fire({ containerId, message: 'New Chat' });
		this._setState(containerId, { allThreads: newThreads, currentThreadId: newThread.id, currentThreadMessages: [] }, true);
		this._storeChatSummary();
		this._addSelectionToChat(containerId);
	}

	private _convertThreadDataFromStorage(threadsStr: string): ChatContainers | ChatThreads | ChatMessage[] {
		const data = JSON.parse(threadsStr, (key, value) => {
			if (value && typeof value === 'object' && value.$mid === 1) {
				return URI.from(value);
			}
			return value;
		});

		if (data && typeof data === 'object') {
			if ('allThreads' in data && 'currentThreadId' in data && !('allContainers' in data)) {
				const oldFormat = data as unknown as ThreadsState;
				const newFormat: ChatContainers = {
					[CODESEEK_VIEW_CONTAINER_ID]: {
						threadsState: {
							allThreads: oldFormat.allThreads || {},
							currentThreadId: oldFormat.currentThreadId || '',
							currentThreadMessages: []
						}
					}
				};
				return newFormat;
			}
		}

		return data;
	}

	private _readAllContainer(): ChatContainers | null {
		try {
			const containerMapStr = this._storageService.get(THREAD_ABSTRACT_STORAGE_KEY, StorageScope.WORKSPACE);
			if (!containerMapStr) {
				return null;
			}
			const containers = this._convertThreadDataFromStorage(containerMapStr) as ChatContainers;
			if (!containers || typeof containers !== 'object') {
				this._codeseekLogService.error('Invalid container structure in storage');
				return null;
			}

			if (!containers[CODESEEK_VIEW_CONTAINER_ID]) {
				containers[CODESEEK_VIEW_CONTAINER_ID] = {
					threadsState: { allThreads: {}, currentThreadId: '', currentThreadMessages: [] }
				};
			}

			for (const containerId of Object.keys(containers)) {
				if (!containers[containerId].threadsState) {
					containers[containerId].threadsState = {
						allThreads: {},
						currentThreadId: '',
						currentThreadMessages: []
					};
				} else {
					containers[containerId].threadsState.currentThreadMessages = [];
					if (!containers[containerId].threadsState.currentThreadId) {
						containers[containerId].threadsState.currentThreadId = '';
					}
				}
			}

			return containers;
		} catch (error) {
			this._codeseekLogService.error('Error reading containers:', error);
			return null;
		}
	}

	private _storeChatSummary() {
		const { allContainers } = this.containerState
		const serializedSummary = JSON.stringify(allContainers);
		this._storageService.store(
			THREAD_ABSTRACT_STORAGE_KEY,
			serializedSummary,
			StorageScope.WORKSPACE,
			StorageTarget.USER
		);
	}

	private _storeChatMessages(containerId: string, threadId: string, messags: ChatMessage[]) {
		const serializedMessages = JSON.stringify(messags)
		this._storageService.store(
			`${THREAD_MESSAGES_STORAGE_KEY}--${containerId}--${threadId}`,
			serializedMessages,
			StorageScope.WORKSPACE,
			StorageTarget.USER
		);
	}

	private _loadChatMessages(containerId: string, threadId: string): ChatMessage[] {
		const messagesStr = this._storageService.get(`${THREAD_MESSAGES_STORAGE_KEY}--${containerId}--${threadId}`, StorageScope.WORKSPACE);
		if (!messagesStr) {
			return [];
		}
		const messages = this._convertThreadDataFromStorage(messagesStr);

		if (messages && Array.isArray(messages)) {
			const firstUserMessage = messages.find(m => m.role === 'user');
			if (firstUserMessage && firstUserMessage.displayContent) {
				this._onDidSetChatTitle.fire({ containerId, message: firstUserMessage.displayContent });
			}
		}

		return messages as ChatMessage[];
	}

	private _deleteThreadMessages(containerId: string, threadId: string): void {
		this._storageService.remove(`${THREAD_MESSAGES_STORAGE_KEY}--${containerId}--${threadId}`, StorageScope.WORKSPACE)
	}

	// this should be the only place this.state = ... appears besides constructor
	private _setState(containerId: string, state: Partial<ThreadsState>, affectsCurrent: boolean) {
		this.containerState.allContainers[containerId].threadsState = {
			...this.containerState.allContainers[containerId].threadsState,
			...state
		};
		if (affectsCurrent)
			this._onDidChangeCurrentThread.fire(containerId);
	}

	private _getSelectionsUpToMessageIdx(containerId: string, messageIdx: number) {
		const prevMessages = this.containerState.allContainers[containerId].threadsState.currentThreadMessages.slice(0, messageIdx);
		return prevMessages.flatMap(m => m.role === 'user' && m.state.stagingSelections || []);
	}

	private _setStreamState(containerId: string, threadId: string, state: Partial<NonNullable<ThreadStreamState[string][string]>>) {
		this.streamState[containerId] ??= {};
		this.streamState[containerId][threadId] = {
			...this.streamState[containerId]?.[threadId],
			...state
		};
		this._onDidChangeStreamState.fire({ containerId, threadId });
	}

	// ---------- streaming ----------
	private _finishStreamingTextMessage = (containerId: string, threadId: string, content: string, error?: { message: string; fullError: Error | null }) => {
		// add assistant's message to chat history, and clear selection
		this._addMessageToThread(containerId, threadId, { role: 'assistant', content, displayContent: content || null });
		this._setStreamState(containerId, threadId, { messageSoFar: undefined, streamingToken: undefined, error, isStreaming: false });
	};

	async editUserMessageAndStreamResponse({ containerId, threadId, userMessage, chatMode, messageIdx }: { containerId: string; threadId: string; userMessage: string; chatMode: ChatMode; messageIdx: number }) {

		const currentThreadMessages = this.containerState.allContainers[containerId].threadsState.currentThreadMessages

		if (currentThreadMessages[messageIdx]?.role !== 'user') {
			throw new Error("Error: editing a message with role !=='user'");
		}

		// get prev and curr selections before clearing the message
		const prevSelns = this._getSelectionsUpToMessageIdx(containerId, messageIdx);
		const currSelns = currentThreadMessages[messageIdx].state.stagingSelections || [];

		// clear messages up to the index
		const slicedMessages = currentThreadMessages.slice(0, messageIdx);
		this._setState(containerId, {
			currentThreadMessages: slicedMessages
		}, true);

		// re-add the message and stream it
		this.addUserMessageAndStreamResponse({ containerId, userMessageOpts: { from: 'Chat', userMessage }, chatMode, chatSelections: { prevSelns, currSelns } });
	}

	private async getCodeBaseSelections(containerId: string, userMessageOpts: userMessageOpts): Promise<CodebaseSelection[]> {
		if (userMessageOpts.from === 'Fix') {
			return [];
		}
		let expandedUserMessage = [userMessageOpts.userMessage];
		if (this._codeseekSettingsService.state.globalSettings.enableLocalCodeBase || this._codeseekSettingsService.state.globalSettings.enableRemoteCodeBase) {
			// expandedUserMessage = await this.expandUserMessage(userMessageOpts.userMessage, this.getCurrentThread().id);
			this._setStreamState(containerId, this.getCurrentThread(containerId).id, { streamingToken: undefined, isStreaming: true });
			const searchResult = await this._codebaseSearchService.semanticSearch(expandedUserMessage.join('\n'));
			this._codeseekLogService.info('Codebase search result:',
				JSON.stringify(searchResult, null, 2), `query: ${expandedUserMessage}`);
			const codebaseSelections: CodebaseSelection[] = searchResult.map(r => ({
				type: 'Codebase' as const,
				fileURI: r.uri,
				title: 'Codebase',
				selectionStr: r.content,
				range: r.range,
				fromMention: false
			}));
			return codebaseSelections;
		}
		return [];
	}

	async expandUserMessage(containerId: string, userMessage: string, threadId: string): Promise<string[]> {
		const messageExpansionResult: string[] = [userMessage];
		let res_: () => void;
		const awaitable = new Promise<void>((res, rej) => { res_ = res; });
		const expandedUserMessageToken = this._llmMessageService.sendLLMMessage({
			containerId,
			useProviderFor: FeatureNames.CtrlL,
			messagesType: 'chatMessages',
			messages: [{ role: 'system', content: messageExpansion_systemMessage }, { role: 'user', content: userMessage }],
			onText: () => { },
			onToolCall: () => { },
			onFinalMessage: (params) => {
				const result = params.fullText.trim();
				const queryMatch = result.match(/<expandedMessage>(.*?)<\/expandedMessage>/s);
				if (queryMatch && queryMatch[1]) {
					messageExpansionResult.push(...queryMatch[1].trim().split(LINE_BREAK_REGEX));
				}

				this._codeseekLogService.info(`messageExpansion, message expansion got result: ${messageExpansionResult}`);
				res_();
			},
			onError: (params) => {
				this._codeseekLogService.error('messageExpansion, message expansion call LLM error:', params.message);
				res_();
			},
			logging: { loggingName: 'messageExpansion' }
		});
		if (expandedUserMessageToken === null) return messageExpansionResult;
		this._setStreamState(containerId, threadId, { streamingToken: expandedUserMessageToken, isStreaming: true });

		await awaitable;
		return messageExpansionResult;
	}

	async addUserMessageAndStreamResponse({ containerId, userMessageOpts, chatMode, chatSelections, agentParamsFromPlugin, taskInfo }: {
		containerId: string;
		userMessageOpts: userMessageOpts;
		chatMode: ChatMode;
		chatSelections?: {
			prevSelns?: StagingSelectionItem[];
			currSelns?: StagingSelectionItem[];
		};
		agentParamsFromPlugin?: Record<string, any>;
		taskInfo?: TaskInfo;
	}) {
		const taskId = taskInfo?.taskId;
		const externalTools = taskInfo?.externalTools;
		const thread = this.getCurrentThread(containerId);
		const threadId = thread.id;

		const currSelns: StagingSelectionItem[] = chatSelections?.currSelns ?? thread.state.stateSelections.list;
		const state = {
			stagingSelections: [...currSelns],
			isBeingEdited: false,
		};

		if (userMessageOpts.userMessage && thread.firstUserMessage === '') {
			this._onDidSetChatTitle.fire({ containerId, message: userMessageOpts.userMessage });
		}

		// add user's message to chat history
		const content = await chat_userMessageContent(
			userMessageOpts,
			currSelns,
			this._codeseekFileService,
			this._codeSeekExporerService,
			this._modelService,
			this._workspaceContextService,
			this._codeseekSettingsService,
			this._urlContentFetcher,
			this._notificationService,
			this._zteUserInfoService,
		);

		const userHistoryElt: ChatMessage = { role: 'user', content, displayContent: userMessageOpts.userMessage, state };
		this._setFirstUserMessage(containerId, threadId, userMessageOpts.userMessage)
		this._addMessageToThread(containerId, threadId, userHistoryElt);

		this._setStreamState(containerId, threadId, { error: undefined });

		const codebaseSelections = await this.getCodeBaseSelections(containerId, userMessageOpts);

		const userMessageContent = await chat_userMessageContent(
			userMessageOpts,
			[...currSelns, ...codebaseSelections],
			this._codeseekFileService,
			this._codeSeekExporerService,
			this._modelService,
			this._workspaceContextService,
			this._codeseekSettingsService,
			this._urlContentFetcher,
			this._notificationService,
			this._zteUserInfoService,
		);

		const internalTools: InternalToolInfo[] | undefined = undefined;
		const agentParamsFromIde = await this._codeseekTaskService.provideIdeParams();
		// agent loop
		const agentLoop = async () => {

			let shouldSendAnotherMessage = true;
			let nMessagesSent = 0;
			const toolCallResult: ToolCallResultType | undefined = undefined;

			while (shouldSendAnotherMessage) {
				shouldSendAnotherMessage = false;
				nMessagesSent += 1;

				let res_: () => void;
				const awaitable = new Promise<void>((res, rej) => { res_ = res; });

				// replace last userMessage with userMessageFullContent (which contains all the files too)
				const messages_ = this.containerState.allContainers[containerId].threadsState.currentThreadMessages.map(m => (toLLMChatMessage(m)));
				const lastUserMsgIdx = findLastIndex(messages_, m => m.role === 'user');
				let messages = messages_;
				if (lastUserMsgIdx !== -1) { // should never be -1
					messages = [
						...messages.slice(0, lastUserMsgIdx),
						{ role: 'user', content: userMessageContent },
						...messages.slice(lastUserMsgIdx + 1, Infinity)];
				}
				const chatMessageEmitters_ = (): SendLLMType => {
					if (chatMode === ChatMode.Agent) {
						return {
							messagesType: 'agentMessages',
							messages: [
								{ role: 'user', content: userMessageContent }
							],
							tools: internalTools,
						};
					}
					else {
						const modelSelection = this._codeseekSettingsService.getModelSelectionForContainer(FeatureNames.CtrlL, containerId);
						let systemMessageContent = '';
						if (userMessageOpts.from === 'Fix') {
							systemMessageContent = fix_systemMessage(modelSelection?.modelName ?? '');
						} else {
							systemMessageContent = chat_systemMessage(modelSelection?.modelName ?? '');
						}
						return {
							messagesType: 'chatMessages',
							messages: [
								{ role: 'system', content: systemMessageContent },
								{ role: 'user', content: user_rules(this._codeseekSettingsService.state.globalSettings.userRules) },
								...messages,
							],
							tools: internalTools,
						};
					}
				};
				const chatMessageEmitters = chatMessageEmitters_();
				this._codeseekLogService.info('Prompt:', JSON.stringify(chatMessageEmitters));
				const startTime = Date.now();
				let firstTokenTime: number | null = null;
				let response = '';
				const reporter = (error?: any) => {
					const firstTokenCostTime = firstTokenTime ? firstTokenTime - startTime : 0;
					const totalCostTime = Date.now() - startTime;
					const request = {
						...chatMessageEmitters,
						useProviderFor: FeatureNames.CtrlL,
						agentParamsFromPlugin,
						agentParamsFromIde: agentParamsFromIde,
					};
					if (chatMode === ChatMode.Ask) {
						this._metricsService.capture(METRICS_EVENT.CHAT, { firstTokenCostTime, totalCostTime, request, response, error });
					} else if (chatMode === ChatMode.Agent) {
						this._metricsService.capture(METRICS_EVENT.AGENT, { firstTokenCostTime, totalCostTime, request, response, error });
					}
				}
				const llmCancelToken = this._llmMessageService.sendLLMMessage({
					containerId,
					...chatMessageEmitters,
					useProviderFor: FeatureNames.CtrlL,
					logging: { loggingName: chatMode },
					agentParamsFromPlugin,
					agentParamsFromIde: agentParamsFromIde,

					onText: ({ fullText }) => {
						firstTokenTime = firstTokenTime ?? Date.now();
						this._setStreamState(containerId, threadId, { messageSoFar: fullText });
						if (taskId) {
							this._codeseekTaskService.fireReceiveMessage(taskId, { message: { fullText: fullText } });
						}
					},
					onToolCall: async ({ fullText, toolCall }) => {
						this._addMessageToThread(containerId, threadId, { role: 'assistant', content: fullText, displayContent: fullText }, false);
						this._setStreamState(containerId, threadId, { messageSoFar: undefined });
						this._setStreamState(containerId, threadId, {
							toolCall: {
								...toolCall,
								content: '',
								result: null,
							}
						});
						const toolCallResult: ToolCallResultType = await this.executeTool(containerId, toolCall, threadId, taskId, externalTools);
						this._setStreamState(containerId, threadId, { toolCall: undefined });
						this._addMessageToThread(containerId, threadId, {
							role: 'tool',
							name: toolCall.name,
							params: toolCall.params,
							id: toolCall.id,
							content: toolCallResult.content,
							result: toolCallResult.result,
							containerId,
							threadId
						});
						this._llmMessageService.updateToolCallResult(llmCancelToken, toolCallResult);
						if (taskId) {
							this._codeseekTaskService.fireReceiveMessage(taskId, { message: { fullText: fullText, toolCall: toolCall, toolCallResult: toolCallResult } });
						}
					},
					onFinalMessage: async ({ fullText, toolCalls }) => {
						this._codeseekLogService.info(`Final message, threadId: ${threadId}, fullText: `,
							fullText, `toolCalls: `, JSON.stringify(toolCalls));
						response = fullText;
						reporter();
						if ((toolCalls?.length ?? 0) === 0) {
							this._finishStreamingTextMessage(containerId, threadId, fullText);
						}
						else {
							this._addMessageToThread(containerId, threadId, { role: 'assistant', content: fullText, displayContent: fullText });
							this._setStreamState(containerId, threadId, { messageSoFar: undefined }); // clear streaming message
							for (const tool of toolCalls ?? []) {
								const toolCallResult: ToolCallResultType = await this.executeTool(containerId, tool, threadId, taskId, externalTools);
								if (toolCallResult.code === ToolCallResultCode.failure) {
									shouldSendAnotherMessage = false;
									break;
								}
								this._addMessageToThread(containerId, threadId, { role: 'tool', name: tool.name, params: tool.params, id: tool.id, content: toolCallResult.content, result: toolCallResult.result, containerId, threadId });
								shouldSendAnotherMessage = true;

							}
						}
						if (taskId) {
							this._codeseekTaskService.fireReceiveMessage(taskId, { message: { fullText: fullText, toolCallResult: toolCallResult } });
							this._codeseekTaskService.fireTaskDone(taskId);
							this._codeseekSettingsService.setChatMode(ChatMode.Ask);
						}
						res_();
					},
					onError: (error) => {
						this._finishStreamingTextMessage(containerId, threadId, this.streamState[containerId]?.[threadId]?.messageSoFar ?? '', error);
						if (taskId) {
							this._codeseekTaskService.fireTaskError(taskId);
							this._codeseekSettingsService.setChatMode(ChatMode.Ask);
						}
						res_();
						reporter(error);
					},
				});
				if (llmCancelToken === null) break;
				this._setStreamState(containerId, threadId, { streamingToken: llmCancelToken, isStreaming: true });

				await awaitable;
			}
		};

		agentLoop(); // DO NOT AWAIT THIS, this fn should resolve when ready to clear inputs

	}

	cancelStreaming(containerId: string, threadId: string) {
		const llmCancelToken = this.streamState[containerId]?.[threadId]?.streamingToken;
		if (llmCancelToken !== undefined) this._llmMessageService.abort(llmCancelToken);
		this._finishStreamingTextMessage(containerId, threadId, this.streamState[containerId]?.[threadId]?.messageSoFar ?? '');
	}

	dismissStreamError(containerId: string, threadId: string): void {
		this._setStreamState(containerId, threadId, { error: undefined });
	}

	async executeTool(containerId: string, toolCall: ToolCallType,
		threadId: string,
		taskId?: string,
		externalTools?: { toolName: string; toolDesc: string; needApprove: boolean; }[]): Promise<ToolCallResultType> {
		const toolName = toolCall.name as ToolName;
		let toolResultVal: ToolCallReturnType[ToolName];
		let content: string = '';
		const ideTool = this._toolsService.toolFns[toolName];

		const onWait = async () => {
			this._setStreamState(containerId, threadId, { isStreaming: false });
			const askMessage: AskMessage = {
				type: 'tool',
				content: toolCall,
			};
			this.getCurrentThread(containerId).state.askMessage = askMessage;
			await pWaitFor(() => this.containerState.allContainers[containerId].threadsState.allThreads[threadId].state.askResponse !== undefined, { interval: 100 });
			const currentThreadState = this.containerState.allContainers[containerId].threadsState.allThreads[threadId].state;
			const response = { type: currentThreadState.askResponse!.type, response: currentThreadState.askResponse?.response, text: currentThreadState.askResponse?.text };
			currentThreadState.askResponse = undefined;
			currentThreadState.askResponseText = undefined;
			return response;
		};
		if (ideTool === undefined) {
			let externalToolsNeedApprove = true;
			const filteredTools = externalTools?.filter(tool => tool.toolName === toolName);
			if (filteredTools && filteredTools.length > 0) {
				externalToolsNeedApprove = filteredTools[0].needApprove;
			}
			if (externalToolsNeedApprove) {
				toolResultVal = await this._toolsService.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.yesButtonClicked && taskId) {
					this._codeseekTaskService.fireToolCall(taskId, toolName, toolCall.params);
				}
				return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
			}
			this._setStreamState(containerId, threadId, { isStreaming: true });
			if (taskId) {
				this._codeseekTaskService.fireToolCall(taskId, toolName, toolCall.params);
				return { code: ToolCallResultCode.success, name: toolName, result: true, error: '', content };
			}
		}

		try {
			if (this._toolsService.isNeedApprove(toolName)) {
				toolResultVal = await this._toolsService.toolFns[ToolNameEnum.APPROVE_REQUEST](toolCall.params as any, onWait);
				content = this._toolsService.toolResultToString[toolName](toolResultVal as any);
				const approveResult = toolResultVal as ApproveRequestResultType;
				if (approveResult.response === AskReponseType.noButtonClicked) {
					return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
				}
			}
			this._setStreamState(containerId, threadId, { isStreaming: true });
			toolResultVal = await ideTool(toolCall.params as any);
			content = this._toolsService.toolResultToString[toolName](toolResultVal as any);
			return { code: ToolCallResultCode.success, name: toolName, result: toolResultVal, error: '', content };
		} catch (error) {
			return { code: ToolCallResultCode.failure, name: toolName, result: null, error: error.message, content };
		}
	}


	// ---------- the rest ----------

	isCurrentThreadWorking(containerId: string): boolean {
		const currentThread = this.getCurrentThread(containerId);
		if (!currentThread) return false;
		const streamState = this.streamState[containerId]?.[currentThread.id];
		return streamState && streamState.streamingToken && !streamState.error ? true : false;
	}

	getCurrentContainerId(): string {
		return this.containerState.currentContainerId;
	}

	getCurrentThreadId(containerId: string): string {
		return this.containerState.allContainers[containerId].threadsState.currentThreadId;
	}

	getCurrentThread(containerId: string): ChatThreads[string] {
		const container = this.containerState.allContainers[containerId];
		if (!container.threadsState.currentThreadId || container.threadsState.currentThreadId === '') {
			return undefined as unknown as ChatThreads[string];
		}
		return container.threadsState.allThreads[container.threadsState.currentThreadId];
	}

	getCurrentThreadMessages(containerId: string): ChatMessage[] {
		return this.containerState.allContainers[containerId].threadsState.currentThreadMessages
	}

	isFocusingContainer(containerId: string): boolean {
		return this.containerState.currentContainerId === containerId;
	}

	getFocusedMessageIdx(containerId: string): number | undefined {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return undefined;

		// get the focusedMessageIdx
		const focusedMessageIdx = thread.state.focusedMessageIdx;
		if (focusedMessageIdx === undefined) return;

		// check that the message is actually being edited
		const focusedMessage = this.getCurrentThreadMessages(containerId)[focusedMessageIdx];
		if (focusedMessage.role !== 'user') return;
		if (!focusedMessage.state) return;

		return focusedMessageIdx;
	}

	isFocusingMessage(containerId: string): boolean {
		return this.getFocusedMessageIdx(containerId) !== undefined;
	}

	switchToContainer(containerId: string): void {
		this.containerState.currentContainerId = containerId;
		this._sidebarStateService.fireFocusContainer(containerId);
		this._onDidChangeCurrentContainer.fire();
		this._onDidChangeCurrentThread.fire(containerId);
	}

	switchToThread(containerId: string, threadId: string, targetContainerId?: string) {
		const actualTargetContainerId = targetContainerId ?? containerId;

		if (!this.containerState.allContainers[actualTargetContainerId]) {
			return;
		}
		const messages = this._loadChatMessages(containerId, threadId);
		if (actualTargetContainerId !== this.containerState.currentContainerId) {
			this.switchToContainer(actualTargetContainerId);
		}
		this._setState(actualTargetContainerId, {
			currentThreadId: threadId,
			currentThreadMessages: messages
		}, true);
	}

	deleteContainer(containerId: string): void {
		if (containerId === CODESEEK_VIEW_CONTAINER_ID) {
			return;
		}

		const currentThreadId = this.getCurrentThreadId(containerId);
		if (currentThreadId) {
			this.cancelStreaming(containerId, currentThreadId);
		}

		const threadsState = this.containerState.allContainers[containerId]?.threadsState;
		if (threadsState) {
			const threadIds = Object.keys(threadsState.allThreads);
			threadIds.forEach(threadId => {
				this._deleteThreadMessages(containerId, threadId);
			});
		}

		const viewContainersRegistry = Registry.as<IViewContainersRegistry>(ViewExtensions.ViewContainersRegistry);
		const viewContainer = viewContainersRegistry.get(containerId);
		if (viewContainer) {
			viewContainersRegistry.deregisterViewContainer(viewContainer);
		}

		if (this.streamState[containerId]) {
			delete this.streamState[containerId];
		}

		delete this.containerState.allContainers[containerId];

		if (this.containerState.currentContainerId === containerId) {
			this.containerState.currentContainerId = CODESEEK_VIEW_CONTAINER_ID;
			this._sidebarStateService.fireFocusContainer(CODESEEK_VIEW_CONTAINER_ID);
			this._onDidChangeCurrentContainer.fire();
			this._onDidChangeCurrentThread.fire(CODESEEK_VIEW_CONTAINER_ID);
		}

		this._storeChatSummary();
	}

	deleteThread(containerId: string, threadId: string) {
		const currentThread = this.getCurrentThread(containerId);
		const isCurrentThread = currentThread?.id === threadId;

		if (isCurrentThread) {
			this.cancelStreaming(containerId, threadId);
		}

		const { allThreads } = this.containerState.allContainers[containerId].threadsState;
		delete allThreads[threadId];
		this._deleteThreadMessages(containerId, threadId)

		// 清理streamState中的相关状态
		if (this.streamState[containerId]?.[threadId]) {
			delete this.streamState[containerId][threadId];
		}

		// 保存更新后的线程集合
		this._storeChatSummary();

		if (isCurrentThread) {
			const remainingThreadIds = Object.keys(allThreads);
			if (remainingThreadIds.length > 0) {
				// 切换到第一个可用的线程
				this.switchToThread(containerId, remainingThreadIds[0]);
			} else {
				// 如果没有可用的线程，创建一个新线程
				this.openNewThread(containerId);
			}
		} else {
			this._setState(containerId, { allThreads }, true);
		}
	}

	private _addSelectionToChat(containerId: string) {
		const selection = this._codeSeekCodeSelectionService.getFileSelction();
		this.addSelectionToChat(containerId, selection);
	}

	addSelectionToChat(containerId: string, selection?: StagingSelectionItem) {
		if (!selection) return;

		const focusedMessageIdx = this.getFocusedMessageIdx(containerId);
		let selections: StagingSelectionItem[] = [];
		let setSelections = (s: StagingSelectionItem[]) => { };

		if (focusedMessageIdx === undefined) {
			selections = this.getCurrentThreadStagingSelections(containerId);
			setSelections = (s: StagingSelectionItem[]) => this.setCurrentThreadStagingSelections(containerId, s);
		} else {
			selections = this.getCurrentMessageState(containerId, focusedMessageIdx).stagingSelections;
			setSelections = (s) => this.setCurrentMessageState(containerId, focusedMessageIdx, { stagingSelections: s });
		}

		const updatedSelections = selections.map(item => {
			if (item.type === 'File' && selection.type === 'File' &&
				item.fileURI.toString() === selection.fileURI.toString() &&
				item.fromActive) {
				return {
					...item,
					fromActive: false,
					fromEditor: true
				};
			}
			return item;
		});

		setSelections(this._mentionsService.addItemToSelectedFile(updatedSelections, selection));
	}

	_addMessageToThread(containerId: string, threadId: string, message: ChatMessage, affectCurrentThread: boolean = true) {
		const { allThreads } = this.containerState.allContainers[containerId].threadsState;

		const oldThread = allThreads[threadId];
		const newMessages = [...this.containerState.allContainers[containerId].threadsState.currentThreadMessages, message]

		// update state and store it
		const newThreads = {
			...allThreads,
			[oldThread.id]: {
				...oldThread,
				lastModified: new Date().toISOString(),
				messagesLength: newMessages.length,
				// messages: [...oldThread.messages, message],
			}
		};
		this._setState(containerId, { allThreads: newThreads, currentThreadMessages: newMessages }, affectCurrentThread); // the current thread just changed (it had a message added to it)
		this._storeChatSummary()
		this._storeChatMessages(containerId, oldThread.id, newMessages);
	}
	private _setFirstUserMessage(containerId: string, threadId: string, userMessage: string) {
		const allThreads = this.containerState.allContainers[containerId].threadsState.allThreads
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId]
		if (thread.firstUserMessage === '') {
			const newThreads = {
				...allThreads,
				[thread.id]: {
					...thread,
					firstUserMessage: userMessage
				}
			}
			this._setState(containerId, { allThreads: newThreads }, false)
		}
	}
	// sets the currently selected message (must be undefined if no message is selected)
	setFocusedMessageIdx(containerId: string, messageIdx: number | undefined) {

		const threadId = this.containerState.allContainers[containerId].threadsState.currentThreadId;
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId];
		if (!thread) return;

		this._setState(containerId, {
			allThreads: {
				...this.containerState.allContainers[containerId].threadsState.allThreads,
				[threadId]: {
					...thread,
					state: {
						...thread.state,
						focusedMessageIdx: messageIdx,
					}
				}
			}
		}, true);
	}

	// set message.state
	private _setCurrentMessageState(containerId: string, state: Partial<UserMessageState>, messageIdx: number): void {

		const threadId = this.containerState.allContainers[containerId].threadsState.currentThreadId;
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId];
		if (!thread) return;

		this._setState(containerId, {
			currentThreadMessages: this.containerState.allContainers[containerId].threadsState.currentThreadMessages.map((m, i) =>
				i === messageIdx && m.role === 'user' ? {
					...m,
					state: {
						...m.state,
						...state
					},
				} : m
			)
		}, true);

	}

	// set thread.state
	private _setCurrentThreadState(containerId: string, state: Partial<ThreadType['state']>): void {

		const threadId = this.containerState.allContainers[containerId].threadsState.currentThreadId;
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId];
		if (!thread) return;

		this._setState(containerId, {
			allThreads: {
				...this.containerState.allContainers[containerId].threadsState.allThreads,
				[thread.id]: {
					...thread,
					state: {
						...thread.state,
						...state
					}
				}
			}
		}, true);

	}

	getCurrentThreadStagingSelections = (containerId: string) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return [];
		return thread.state.stateSelections.list;
	};

	getCurrentThreadStateSelections = (containerId: string) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return { list: [], followEditorActive: true };
		return thread.state.stateSelections;
	};

	setCurrentThreadStagingSelections = (containerId: string, stagingSelections: StagingSelectionItem[]) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return;
		const stateSelections = thread.state.stateSelections;
		stateSelections.list = [...stagingSelections];
		this._setCurrentThreadState(containerId, { stateSelections });
	};

	// gets `staging` and `setStaging` of the currently focused element, given the index of the currently selected message (or undefined if no message is selected)

	getCurrentMessageState = (containerId: string, messageIdx: number): UserMessageState => {
		const currMessage = this.containerState.allContainers[containerId].threadsState.currentThreadMessages[messageIdx];
		if (!currMessage || currMessage.role !== 'user') return defaultMessageState;
		return currMessage.state;
	}

	setCurrentMessageState = (containerId: string, messageIdx: number, newState: Partial<UserMessageState>) => {
		const currMessage = this.containerState.allContainers[containerId].threadsState.currentThreadMessages[messageIdx];
		if (!currMessage || currMessage.role !== 'user') return;
		this._setCurrentMessageState(containerId, newState, messageIdx);
	}

	setCurrentThreadStateSelectionsChangeSelections = (containerId: string) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return;
		const stagingSelections = thread.state.stateSelections;
		stagingSelections.followEditorActive = false;
	}

	private onDidActiveEditorChange(containerId: string) {
		const selections = this.getCurrentThreadStagingSelections(containerId);
		if (selections.length === 0) {
			this._addSelectionToChat(containerId);
		} else {
			if (this.containerState.allContainers[containerId].threadsState.currentThreadMessages.length === 0) {
				const selection = this._codeSeekCodeSelectionService.getFileSelction();
				if (!selection) {
					return;
				}

				let hasSameUriFromEditor = false;
				const filteredSelections = selections.filter(s => {
					if (s.type === 'File') {
						if (s.fileURI.toString() === selection.fileURI.toString() && s.fromEditor) {
							hasSameUriFromEditor = true;
							return true;
						}
						return !s.fromActive;
					}
					return true;
				});
				if (!hasSameUriFromEditor) {
					this.setCurrentThreadStagingSelections(containerId, [selection, ...filteredSelections]);
				} else {
					this.setCurrentThreadStagingSelections(containerId, filteredSelections);
				}
			}
		}
	}

	setAskResponse(containerId: string, askResponse: AskResponse): void {
		const currentThreadState = this.getCurrentThread(containerId).state;
		currentThreadState.askResponse = { type: askResponse.type, response: askResponse.response, text: askResponse.text };
	}

	private _registerExistingContainers(): void {
		const viewContainersRegistry = Registry.as<IViewContainersRegistry>(ViewExtensions.ViewContainersRegistry);
		const viewsRegistry = Registry.as<IViewsRegistry>(ViewExtensions.ViewsRegistry);

		const containerIds = Object.keys(this.containerState.allContainers);

		for (const containerId of containerIds) {
			if (containerId === CODESEEK_VIEW_CONTAINER_ID) {
				continue;
			}

			const orderStr = containerId.split('.')[3];
			const orderNum = parseInt(orderStr);
			if (isNaN(orderNum)) {
				continue;
			}

			const existingContainer = viewContainersRegistry.get(containerId);
			if (existingContainer) {
				continue;
			}

			const newContainer = viewContainersRegistry.registerViewContainer({
				id: containerId,
				title: localize2('newCodeseekContainer', 'New Chat'),
				ctorDescriptor: new SyncDescriptor(
					ViewPaneContainer,
					[containerId, {
						mergeViewWithContainerWhenSingleView: true,
						orientation: Orientation.HORIZONTAL,
					}]
				),
				hideIfEmpty: false,
				order: orderNum,
			}, ViewContainerLocation.AuxiliaryBar, { doNotRegisterOpenCommand: true, isDefault: true });

			viewsRegistry.registerViews([{
				id: containerId,
				hideByDefault: false,
				name: localize2('newCodeseekChat', 'New Chat'),
				ctorDescriptor: new SyncDescriptor(SidebarViewPane),
				canToggleVisibility: false,
				canMoveView: false,
				weight: 80,
				order: 1,
			}], newContainer);

			const threadsState = this.containerState.allContainers[containerId].threadsState;
			const threadIds = Object.keys(threadsState.allThreads);

			if (threadIds.length > 0 && (!threadsState.currentThreadId || threadsState.currentThreadId === '')) {
				const firstThreadId = threadIds[0];
				const messages = this._loadChatMessages(containerId, firstThreadId);
				this._setState(containerId, {
					currentThreadId: firstThreadId,
					currentThreadMessages: messages
				}, false);
			} else if (threadIds.length === 0) {
				this.openNewThread(containerId);
			}
		}

		const maxOrder = containerIds.reduce((max, id) => {
			const orderStr = id.split('.')[3];
			const orderNum = parseInt(orderStr);
			return isNaN(orderNum) ? max : Math.max(max, orderNum);
		}, order);

		order = Math.max(order, maxOrder + 1);
	}
}

registerSingleton(IChatThreadService, ChatThreadService, InstantiationType.Eager);
