import { Disposable } from '../../../../base/common/lifecycle.js';
import { ICodeEditorService } from '../../../../editor/browser/services/codeEditorService.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { getContentInRange } from './helpers/readFile.js';
import { FileSelection, StagingSelectionItem } from '../common/selectedFileService.js';
import { roundRangeToLines } from './helpers/util.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { URI } from '../../../../base/common/uri.js';
import { IMarker } from '../../../../platform/markers/common/markers.js';
import { IRange } from '../../../../editor/common/core/range.js';
import { basename } from '../../../../base/common/resources.js';
export const ICodeseekCodeSelectionService = createDecorator<ICodeseekCodeSelectionService>('CodeseekCodeSelectionService');

export interface ICodeseekCodeSelectionService {
	readonly _serviceBrand: undefined;
	getFileSelction(): StagingSelectionItem | undefined;
	getCodeSelectionFromMarker(marker: IMarker, upLineCount?: number, downLineCount?: number): StagingSelectionItem | undefined;
	formatCodeWithMarker(codeSelection: StagingSelectionItem, marker: IMarker): string | undefined;
	formatCodeWithMarkers(codeSelection: StagingSelectionItem, markers: IMarker[]): string | undefined;
	mergeCodeSelections(codeSelections: StagingSelectionItem[]): StagingSelectionItem[];

	onDidAddContentFromEditor: Event<FileSelection>;
	onDidAddCodeBlock: Event<StagingSelectionItem>;
	addContentFromEditor(uri: URI): void;
	addCodeBlock(): void;
}

class CodeseekCodeSelectionService extends Disposable implements ICodeseekCodeSelectionService {
	readonly _serviceBrand: undefined;

	private readonly _onDidAddContentFromEditor = new Emitter<FileSelection>();
	readonly onDidAddContentFromEditor: Event<FileSelection> = this._onDidAddContentFromEditor.event;

	private readonly _onDidAddCodeBlock = new Emitter<StagingSelectionItem>();
	readonly onDidAddCodeBlock: Event<StagingSelectionItem> = this._onDidAddCodeBlock.event;

	constructor(
		@ICodeEditorService private readonly _codeEditorService: ICodeEditorService,
	) {
		super();
	}

	addContentFromEditor(uri: URI) {
		const selection: FileSelection = {
			type: 'File',
			fileURI: uri,
			title: basename(uri),
			selectionStr: null,
			range: null,
			fromActive: false,
			fromMention: false,
			fromEditor: true,
		};
		this._onDidAddContentFromEditor.fire(selection);
	}

	addCodeBlock(): void {
		const model = this._codeEditorService.getActiveCodeEditor()?.getModel();
		if (!model)
			return undefined;

		const editor = this._codeEditorService.getActiveCodeEditor();
		const selectionRange = roundRangeToLines(editor?.getSelection(), { emptySelectionBehavior: 'null' });
		const selectionStr = getContentInRange(model, selectionRange);

		const selection: StagingSelectionItem = !selectionRange || !selectionStr || (selectionRange.startLineNumber > selectionRange.endLineNumber) ? {
			type: 'File',
			fileURI: model.uri,
			title: basename(model.uri),
			selectionStr: null,
			range: null,
			fromActive: true,
			fromMention: false,
			fromEditor: false,
		} : {
			type: 'Selection',
			fileURI: model.uri,
			title: basename(model.uri),
			selectionStr: selectionStr,
			range: selectionRange,
			fromMention: false,
		};

		this._onDidAddCodeBlock.fire(selection);
	}

	getFileSelction(): StagingSelectionItem | undefined {
		const model = this._codeEditorService.getActiveCodeEditor()?.getModel();
		if (!model)
			return undefined;

		const selection: FileSelection = {
			type: 'File',
			fileURI: model.uri,
			title: basename(model.uri),
			selectionStr: null,
			range: null,
			fromActive: true,
			fromMention: false,
			fromEditor: false,
		};

		return selection;
	}

	formatCodeWithMarker(codeSelection: StagingSelectionItem, marker: IMarker): string | undefined {
		return this.formatCodeWithMarkers(codeSelection, [marker]);
	}

	formatCodeWithMarkers(codeSelection: StagingSelectionItem, markers: IMarker[]): string | undefined {
		if (codeSelection.type !== 'Selection' || !codeSelection.selectionStr || !codeSelection.range) {
			return undefined;
		}

		const lines = codeSelection.selectionStr.split('\n');
		const formattedLines: string[] = [];

		// 创建一个按行号索引的错误消息映射
		const errorsByLine: Map<number, IMarker[]> = new Map();
		for (const marker of markers) {
			// 确定在代码选择范围内显示错误信息的行号
			let errorLineNumber = marker.startLineNumber;

			// 如果错误起始行在选择范围之前，则将错误显示在选择的第一行
			if (errorLineNumber < codeSelection.range.startLineNumber) {
				errorLineNumber = codeSelection.range.startLineNumber;
			}
			// 如果错误起始行在选择范围之后，则将错误显示在选择的最后一行
			else if (errorLineNumber > codeSelection.range.endLineNumber) {
				errorLineNumber = codeSelection.range.endLineNumber;
			}

			const lineMarkers = errorsByLine.get(errorLineNumber) || [];
			lineMarkers.push(marker);
			errorsByLine.set(errorLineNumber, lineMarkers);
		}

		for (let i = 0; i < lines.length; i++) {
			const lineNumber = codeSelection.range.startLineNumber + i;
			const paddedLineNumber = String(lineNumber).padEnd(3);

			// 添加代码行
			formattedLines.push(`${paddedLineNumber} | ${lines[i]}`);

			// 检查是否有该行的错误消息
			const lineMarkers = errorsByLine.get(lineNumber);
			if (lineMarkers && lineMarkers.length > 0) {
				// 添加该行的所有错误消息
				for (const marker of lineMarkers) {
					const errorLabel = "Err ".padEnd(3);
					formattedLines.push(`${errorLabel} | ${marker.message}`);
				}
			}
		}

		return formattedLines.join('\n');
	}

	mergeCodeSelections(codeSelections: StagingSelectionItem[]): StagingSelectionItem[] {
		if (!codeSelections.length) {
			return [];
		}

		if (codeSelections.length === 1) {
			return [codeSelections[0]];
		}

		// 确保所有codeSelection都是Selection类型
		const selections = codeSelections.filter(s => s.type === 'Selection' && s.selectionStr && s.range);
		if (!selections.length) {
			return [];
		}

		// 使用并查集算法合并有交集的代码块
		const parent = new Map<number, number>();
		// 初始化，每个节点的父节点是自己
		for (let i = 0; i < selections.length; i++) {
			parent.set(i, i);
		}

		// 查找根节点
		const find = (x: number): number => {
			if (parent.get(x) !== x) {
				parent.set(x, find(parent.get(x)!));
			}
			return parent.get(x)!;
		};

		// 合并两个集合
		const union = (x: number, y: number) => {
			parent.set(find(x), find(y));
		};

		// 检查两个代码块是否有交集
		const hasIntersection = (a: StagingSelectionItem, b: StagingSelectionItem): boolean => {
			if (a.type !== 'Selection' || b.type !== 'Selection' || !a.range || !b.range) {
				return false;
			}

			// 检查是否是同一个文件
			if (a.fileURI.toString() !== b.fileURI.toString()) {
				return false;
			}

			// 检查行范围是否有交集
			return !(a.range.endLineNumber < b.range.startLineNumber || a.range.startLineNumber > b.range.endLineNumber);
		};

		// 合并有交集的代码块
		for (let i = 0; i < selections.length; i++) {
			for (let j = i + 1; j < selections.length; j++) {
				if (hasIntersection(selections[i], selections[j])) {
					union(i, j);
				}
			}
		}

		// 根据并查集结果对代码块进行分组
		const groups = new Map<number, number[]>();
		for (let i = 0; i < selections.length; i++) {
			const root = find(i);
			if (!groups.has(root)) {
				groups.set(root, []);
			}
			groups.get(root)!.push(i);
		}

		// 对每个分组进行合并
		const model = this._codeEditorService.getActiveCodeEditor()?.getModel();
		if (!model) {
			return selections;
		}

		const result: StagingSelectionItem[] = [];
		for (const indices of groups.values()) {
			if (indices.length === 1) {
				// 单个元素的组，直接加入结果
				result.push(selections[indices[0]]);
				continue;
			}

			// 计算合并范围
			let minLine = Number.MAX_SAFE_INTEGER;
			let maxLine = 0;
			const fileURI = selections[indices[0]].fileURI;

			for (const idx of indices) {
				const selection = selections[idx];
				if (selection.type !== 'Selection' || !selection.range) {
					continue;
				}

				minLine = Math.min(minLine, selection.range.startLineNumber);
				maxLine = Math.max(maxLine, selection.range.endLineNumber);
			}

			// 创建合并的范围
			const mergedRange: IRange = {
				startLineNumber: minLine,
				endLineNumber: maxLine,
				startColumn: 1,
				endColumn: Number.MAX_SAFE_INTEGER
			};

			// 获取合并范围内的代码内容
			const selectionStr = getContentInRange(model, mergedRange, false);
			if (!selectionStr) {
				continue;
			}

			// 创建合并后的Selection
			const mergedSelection: StagingSelectionItem = {
				type: 'Selection',
				title: basename(model.uri),
				fileURI: fileURI,
				selectionStr: selectionStr,
				range: mergedRange,
				fromMention: false,
			};

			result.push(mergedSelection);
		}

		return result;
	}

	getCodeSelectionFromMarker(marker: IMarker, upLineCount?: number, downLineCount?: number): StagingSelectionItem | undefined {
		const model = this._codeEditorService.getActiveCodeEditor()?.getModel();
		if (!model)
			return undefined;
		const selectionRange = {
			startLineNumber: marker.startLineNumber - (upLineCount || 1),
			endLineNumber: marker.endLineNumber + (downLineCount || 2),
			startColumn: 1,
			endColumn: Number.MAX_SAFE_INTEGER
		};
		const selectionStr = getContentInRange(model, selectionRange, false);
		if (!selectionStr || !selectionRange) return undefined;
		const selection: StagingSelectionItem = {
			type: 'Selection',
			fileURI: model.uri,
			title: basename(model.uri),
			selectionStr: selectionStr,
			range: selectionRange,
			fromMention: false,
		};
		return selection;
	}
}

registerSingleton(ICodeseekCodeSelectionService, CodeseekCodeSelectionService, InstantiationType.Delayed);
