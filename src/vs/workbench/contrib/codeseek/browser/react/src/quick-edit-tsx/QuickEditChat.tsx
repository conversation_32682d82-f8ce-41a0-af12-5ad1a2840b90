/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, { FormEvent, useCallback, useEffect, useRef, useState } from 'react';
import { useSettingsState, useAccessor, useCtrlKZoneStreamingState } from '../util/services.js';
import { TextAreaFns, CodeseekInputBox2 } from '../util/inputs.js';
import { QuickEditPropsType } from '../../../quickEditActions.js';
import { CodeseekChatArea } from '../sidebar-tsx/SidebarChat.js';
import { CODESEEK_CTRL_K_ACTION_ID } from '../../../actionIDs.js';
import { useRefState } from '../util/helpers.js';
import { useScrollbarStyles } from '../util/useScrollbarStyles.js';
import { FeatureNames, isFeatureNameDisabled } from '../../../../../../../workbench/contrib/codeseek/common/codeseekSettingsTypes.js';
import { StagingSelectionItem } from '../../../../common/selectedFileService.js';
import { ContextMenuOptionType, ContextMenuQueryItem } from '../contextMenu/context-mentions.js';

export const QuickEditChat = ({
	diffareaid,
	onChangeHeight,
	onChangeText: onChangeText_,
	textAreaRef: textAreaRef_,
	onClose: onClose_,
	initText,
	selections,
	setSelections,
}: QuickEditPropsType) => {

	const accessor = useAccessor()
	const editCodeService = accessor.get('IEditCodeService')
	const quickEditStateService = accessor.get('IQuickEditStateService')
	const sizerRef = useRef<HTMLDivElement | null>(null)
	const textAreaRef = useRef<HTMLTextAreaElement | null>(null)
	const textAreaFnsRef = useRef<TextAreaFns | null>(null)
	const [stagingSelections, setStagingSelections] = useState<StagingSelectionItem[]>(selections)
	const [isShowContextMenu, setIsShowContextMenu] = useState(false)

	// 组件挂载时触发聚焦事件
	useEffect(() => {
		quickEditStateService.fireFocusChat();
		return () => {
			quickEditStateService.fireBlurChat();
		};
	}, [quickEditStateService]);

	useEffect(() => {
		const inputContainer = sizerRef.current
		if (!inputContainer) return;
		// only observing 1 element
		let resizeObserver: ResizeObserver | undefined
		resizeObserver = new ResizeObserver((entries) => {
			const height = entries[0].borderBoxSize[0].blockSize
			onChangeHeight(height)
		})
		resizeObserver.observe(inputContainer);
		return () => { resizeObserver?.disconnect(); };
	}, [onChangeHeight]);

	const settingsState = useSettingsState()

	// state of current message
	const [instructionsAreEmpty, setInstructionsAreEmpty] = useState(!(initText ?? '')) // the user's instructions
	const isDisabled = instructionsAreEmpty || !!isFeatureNameDisabled(FeatureNames.CtrlK, settingsState)


	const [isStreamingRef, setIsStreamingRef] = useRefState(editCodeService.isCtrlKZoneStreaming({ diffareaid }))
	useCtrlKZoneStreamingState(useCallback((diffareaid2, isStreaming) => {
		if (diffareaid !== diffareaid2) return
		setIsStreamingRef(isStreaming)
	}, [diffareaid, setIsStreamingRef]))


	const onSubmit = useCallback(() => {
		if (isDisabled) return
		if (isStreamingRef.current) return
		textAreaFnsRef.current?.disable()

		editCodeService.startApplying({
			from: 'QuickEdit',
			type: 'rewrite',
			diffareaid,
		})
	}, [isStreamingRef, isDisabled, editCodeService, diffareaid])

	const onInterrupt = useCallback(() => {
		if (!isStreamingRef.current) return
		editCodeService.interruptCtrlKStreaming({ diffareaid })
		textAreaFnsRef.current?.enable()
	}, [isStreamingRef, editCodeService])


	const onX = useCallback(() => {
		onInterrupt()
		editCodeService.removeCtrlKZone({ diffareaid })
		onClose_()
	}, [editCodeService, diffareaid, onClose_])

	useScrollbarStyles(sizerRef)
	const onSelectContextMenu = useCallback((option: ContextMenuQueryItem) => {
		if (option.type === ContextMenuOptionType.File) {
			setIsShowContextMenu(false)
		}
	}, [setSelections, setIsShowContextMenu])
	const onShowContextMenu = useCallback(() => {
		setIsShowContextMenu(!isShowContextMenu)
	}, [setIsShowContextMenu, isShowContextMenu])

	const chatAreaRef = useRef<HTMLDivElement | null>(null)
	return <div ref={sizerRef} style={{ maxWidth: 450 }} className={`py-2 w-full`}>
		<CodeseekChatArea
			divRef={chatAreaRef}
			onSubmit={onSubmit}
			onAbort={onInterrupt}
			onClose={onX}
			isStreaming={isStreamingRef.current}
			showModeDropdown={false}
			isDisabled={isDisabled}
			featureName={FeatureNames.CtrlK}
			className="px-2 py-1 w-full"
			showSelections={stagingSelections.length > 0}
			selections={stagingSelections}
			setSelections={(list) => {
				setStagingSelections(list);
				setSelections(list)
			}}
			onClickAnywhere={() => {
				textAreaRef.current?.focus();
				quickEditStateService.fireFocusChat();
			}}
			onShowContextMenu={onShowContextMenu}
		>
			<CodeseekInputBox2
				className='px-1 min-h-[20px] max-w-[430px]'
				initValue={initText}
				ref={useCallback((r: HTMLTextAreaElement | null) => {
					textAreaRef.current = r
					textAreaRef_(r)
					r?.addEventListener('keydown', (e) => {
						if (e.key === 'Escape')
							onX()
					})
					// 文本框聚焦时触发事件
					r?.addEventListener('focus', () => {
						quickEditStateService.fireFocusChat();
					})
				}, [textAreaRef_, onX, quickEditStateService])}
				fnsRef={textAreaFnsRef}
				placeholder="Enter instructions..."
				onChangeText={useCallback((newStr: string) => {
					setInstructionsAreEmpty(!newStr)
					onChangeText_(newStr)
				}, [onChangeText_])}
				onKeyDown={(e) => {
					if (e.key === 'Enter' && !e.shiftKey) {
						onSubmit()
						return
					}
					if (e.key === 'Escape') {
						onClose_()
						return
					}
				}}
				selections={stagingSelections}
				setSelections={(list) => {
					setStagingSelections(list);
					setSelections(list);
				  }}
				isShowContextMenu={isShowContextMenu}
				multiline={true}
				onSelectContextMenu={onSelectContextMenu}
			/>
		</CodeseekChatArea>
	</div>


}
