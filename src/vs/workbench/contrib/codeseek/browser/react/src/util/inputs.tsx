/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, { ChangeEvent, forwardRef, useCallback, useEffect, useId, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { IInputBoxStyles, InputBox } from '../../../../../../../base/browser/ui/inputbox/inputBox.js';
import { defaultCheckboxStyles, defaultInputBoxStyles, defaultSelectBoxStyles } from '../../../../../../../platform/theme/browser/defaultStyles.js';
import { SelectBox } from '../../../../../../../base/browser/ui/selectBox/selectBox.js';
import { IDisposable } from '../../../../../../../base/common/lifecycle.js';
import { Checkbox } from '../../../../../../../base/browser/ui/toggle/toggle.js';

import { CodeEditorWidget } from '../../../../../../../editor/browser/widget/codeEditor/codeEditorWidget.js'
import { useAccessor } from './services.js';
import { ITextModel } from '../../../../../../../editor/common/model.js';
import { useFloating, autoUpdate, offset, flip, shift, size } from '@floating-ui/react';
import { ContextMenuOptionType, ContextMenuQueryItem, getContextMenuOptions, insertMention, shouldShowContextMenu } from '../contextMenu/context-mentions.js'
import  ContextMenu  from '../contextMenu/ContextMenu.js'
import { FilePathItem } from '../../../../common/codeseekExporerService.js';
import { highLightMentionsFormater, mentionRegex } from '../shared/context-mentions.js';
import { ChatMode } from '../../../../common/codeseekSettingsService.js';
import { FileSelection, FolderSelection, StagingSelectionItem } from '../../../../common/selectedFileService.js';
import { basename } from '../../../../../../../base/common/resources.js';


// type guard
const isConstructor = (f: any)
	: f is { new(...params: any[]): any } => {
	return !!f.prototype && f.prototype.constructor === f;
}

export const WidgetComponent = <CtorParams extends any[], Instance>({ ctor, propsFn, dispose, onCreateInstance, children, className }
	: {
		ctor: { new(...params: CtorParams): Instance } | ((container: HTMLDivElement) => Instance),
		propsFn: (container: HTMLDivElement) => CtorParams, // unused if fn
		onCreateInstance: (instance: Instance) => IDisposable[],
		dispose: (instance: Instance) => void,
		children?: React.ReactNode,
		className?: string
	}
) => {
	const containerRef = useRef<HTMLDivElement | null>(null);

	useEffect(() => {
		const instance = isConstructor(ctor) ? new ctor(...propsFn(containerRef.current!)) : ctor(containerRef.current!)
		const disposables = onCreateInstance(instance);
		return () => {
			disposables.forEach(d => d.dispose());
			dispose(instance)
		}
	}, [ctor, propsFn, dispose, onCreateInstance, containerRef])

	return <div ref={containerRef} className={className === undefined ? `w-full` : className}>{children}</div>
}


export type TextAreaFns = { setValue: (v: string) => void, enable: () => void, disable: () => void }
type InputBox2Props = {
	initValue?: string | null;
	placeholder: string;
	multiline: boolean;
	fnsRef?: { current: null | TextAreaFns };
	className?: string;
	selections?: StagingSelectionItem[]
	onChangeText?: (value: string) => void;
	onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
	onFocus?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
	onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
	onChangeHeight?: (newHeight: number) => void;
	onSelectContextMenu?: (option: ContextMenuQueryItem) => void;
	setSelections?: (s: StagingSelectionItem[]) => void;
	enabled?: boolean;
	isShowContextMenu?: boolean;
	enableMention?: boolean;
}


type TextAreaSelRange = {
	start: number;
	end: number;
}
export const CodeseekInputBox2 = forwardRef<HTMLTextAreaElement, InputBox2Props>(function X({
	initValue, placeholder, multiline, fnsRef, className, selections,
	onKeyDown, onFocus, onBlur, onChangeText, onSelectContextMenu, setSelections,
	enabled=true, isShowContextMenu=false, enableMention=true }, ref) {

	// mirrors whatever is in ref
	const textAreaRef = useRef<HTMLTextAreaElement | null>(null)
	const [isEnabled, setEnabled] = useState(true)
	const accessor = useAccessor();
    const textAreaService = accessor.get('ITextAreaService');
	const mentionService = accessor.get('IMentionService')

	const [openRecords, setOpenRecords] = useState<FilePathItem[]>([])
	const [selectedType, setSelectedType] = useState<ContextMenuOptionType | null>(null)
	const [thumbnailsHeight, setThumbnailsHeight] = useState(0)
	const [showContextMenu, setShowContextMenu] = useState(false)
	const [cursorPosition, setCursorPosition] = useState(0)
	const [searchQuery, setSearchQuery] = useState("")
	const [isMouseDownOnMenu, setIsMouseDownOnMenu] = useState(false)
	const highlightLayerRef = useRef<HTMLDivElement>(null)
	const [mouseSelectedMenuIndex, setMouseSelectedIndex] = useState(-1)
	const [arrowSelectedIndex, setArrowSelectedIndex] = useState(-1)
	const [justDeletedSpaceAfterMention, setJustDeletedSpaceAfterMention] = useState(false)
	const [intendedCursorPosition, setIntendedCursorPosition] = useState<number | null>(null)
	const contextMenuContainerRef = useRef<HTMLDivElement>(null)
	const [inputValue, setInputValue] = useState("");
	const [textAreaRect, setTextAreaRect] = useState<DOMRect>()
	const textareaSelRange = useRef<TextAreaSelRange | null>(null)


	useEffect(() => {
		const codeSeekExplorerService = accessor.get('ICodeSeekExporerService')

		codeSeekExplorerService.onOpenRecordsChange(() => {
			setOpenRecords(codeSeekExplorerService.getOpenRecords());
		  });
		setOpenRecords(codeSeekExplorerService.getOpenRecords())
	}, []);

	const adjustHeight = useCallback(() => {
		const r = textAreaRef.current
		if (!r) return

		r.style.height = 'auto' // set to auto to reset height, then set to new height

		if (r.scrollHeight === 0) return requestAnimationFrame(adjustHeight)
		const h = r.scrollHeight
		const newHeight = Math.min(h + 1, 500) // plus one to avoid scrollbar appearing when it shouldn't
		r.style.height = `${newHeight}px`
	}, []);



	const fns: TextAreaFns = useMemo(() => ({
		setValue: (val) => {
			const r = textAreaRef.current
			if (!r) return
			r.value = val
			onChangeText?.(r.value)
			adjustHeight()
		},
		enable: () => { setEnabled(true) },
		disable: () => { setEnabled(false) },
	}), [onChangeText, adjustHeight])


	useLayoutEffect(() => {
		if (intendedCursorPosition !== null && textAreaRef.current) {
			textAreaRef.current.setSelectionRange(intendedCursorPosition, intendedCursorPosition)
			setIntendedCursorPosition(null) // Reset the state
		}
	}, [inputValue, intendedCursorPosition])

	useEffect(() => {
		if (initValue)
			fns.setValue(initValue)
	}, [initValue])

	useEffect(() => {
		if (!showContextMenu) {
			setSelectedType(null)
			setMouseSelectedIndex(0)
			setArrowSelectedIndex(0)
		}
	}, [showContextMenu])

	useEffect(() => {
		enableMention && setShowContextMenu(isShowContextMenu)
	}, [isShowContextMenu])


	useEffect(() => {
        textAreaService.setTextAreaRef(textAreaRef.current);

        return () => {
            textAreaService.setTextAreaRef(null);
        }
    }, [textAreaService])

	const onChange = useCallback((e: ChangeEvent<HTMLTextAreaElement>) => {
		const r = textAreaRef.current
		if (!r) return
		const newValue = r.value
		const newCursorPosition = r.selectionStart
		updateInputValue(newValue)
		setCursorPosition(cursorPosition)
		const showMenu = shouldShowContextMenu(newValue, newCursorPosition)

		enableMention && setShowContextMenu(showMenu)

		onChangeText?.(newValue)

		if (showMenu) {
			if (newValue.startsWith("/")) {
				// Handle slash command
				const query = newValue
				setSearchQuery(query)
			} else {
				// Existing @ mention handling
				const lastAtIndex = newValue.lastIndexOf("@", newCursorPosition - 1)
				const query = newValue.slice(lastAtIndex + 1, newCursorPosition)
				setSearchQuery(query)
			}
			setArrowSelectedIndex(0)
			setMouseSelectedIndex(0)
		} else {
			setSearchQuery("")
			setArrowSelectedIndex(-1)
			setMouseSelectedIndex(-1)
		}
		adjustHeight()
	}, [onChangeText, adjustHeight])

	const queryItems = useMemo(() => {
		return [
			...openRecords
				.filter((openRecord) => openRecord.isActive)
				.map((openRecord) => ({
					type: ContextMenuOptionType.File,
					value: openRecord.relativePath,
					uri: openRecord.uri,
					label: openRecord.relativePath.split('/').pop(),
				})),
			// ...openRecords
			// 	.filter((openRecord) => openRecord.isDirectory)
			// 	.map((openRecord) => ({
			// 		type: ContextMenuOptionType.Folder,
			// 		value: openRecord.relativePath,
			// 		uri: openRecord.uri,
			// 		label: openRecord.relativePath.split('/').pop(),
			// 	})),
			...openRecords
				.filter((openRecord) => !openRecord.isDirectory && !openRecord.isActive)
				.map((openRecord) => ({
					type: ContextMenuOptionType.File,
					value: openRecord.relativePath,
					uri: openRecord.uri,
					label: openRecord.relativePath.split('/').pop(),
				})),
		]
	}, [openRecords])

	const handleMentionSelect = useCallback(
		(option: ContextMenuQueryItem) => {
			const type = option.type;
			const value = option.label;
			if (type === ContextMenuOptionType.NoResults) {
				return
			}

			if (
				type === ContextMenuOptionType.File ||
				type === ContextMenuOptionType.Folder ||
				type === ContextMenuOptionType.Git ||
				type === ContextMenuOptionType.ExplorerView
			) {
				if (!value) {
					setSelectedType(type)
					setSearchQuery("")
					setMouseSelectedIndex(0)
					setArrowSelectedIndex(0)
					return
				}
			}

			setShowContextMenu(false)
			setSelectedType(null)
			if (textAreaRef.current) {
				if (!isShowContextMenu) {
					let insertValue = value || ""
					if (type === ContextMenuOptionType.File || type === ContextMenuOptionType.Folder) {
						insertValue = value || ""
					} else if (type === ContextMenuOptionType.Problems) {
						insertValue = "problems"
					} else if (type === ContextMenuOptionType.Git) {
						insertValue = value || ""
					} else if (type === ContextMenuOptionType.Codebase) {
						insertValue = "Codebase"
					}

					const { newValue, mentionIndex } = insertMention(
						textAreaRef.current.value,
						cursorPosition,
						insertValue,
					)

					updateInputValue(newValue)
					const newCursorPosition = newValue.indexOf(" ", mentionIndex + insertValue.length) + 1
					setCursorPosition(newCursorPosition)
					setIntendedCursorPosition(newCursorPosition)
				}


				handleSelectContextMenu(option)
				// scroll to cursor
				setTimeout(() => {
					if (textAreaRef.current) {
						textAreaRef.current.blur()
						textAreaRef.current.focus()
					}
				}, 0)
			}
		},
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[cursorPosition, isShowContextMenu],
	)

	const handleSelectContextMenu = useCallback((option: ContextMenuQueryItem) => {
		switch (option.type) {
			case ContextMenuOptionType.File:
				if( option.uri) {
					const selection: FileSelection = {
						type: 'File',
						fileURI: option.uri,
						title: basename(option.uri),
						selectionStr: null,
						range: null,
						fromMention: true,
						fromActive: false,
						fromEditor: false,
					}

					setSelections?.(mentionService.addItemToSelectedFile(selections, selection))
				}
				break;
			case ContextMenuOptionType.Folder:
				if(option.uri) {
					const selection: FolderSelection = {
						type: 'Folder',
						fileURI: option.uri,
						title: basename(option.uri),
						selectionStr: null,
						range: null,
						fromMention: true,
					}

					setSelections?.(mentionService.addItemToSelectedFile(selections, selection))
				}
				break;
			case ContextMenuOptionType.Codebase:
				// const selection: CodebaseSelection = {
				// 	type: 'Codebase',
				// 	fileURI: undefined,
				// 	selectionStr: null,
				// 	range: null,
				// 	fromMention: true,
				// }
				// setSelections(addItemToSelectedFile(selections, selection))
				break;
			default:
				break;
		}
		onSelectContextMenu?.(option)
	}, [setSelections, selections, onSelectContextMenu])

	const handlePaste = useCallback(
		async (e: React.ClipboardEvent) => {

			let startPos = cursorPosition;
			let endPos = cursorPosition;
			if(textareaSelRange.current) {
				const range = textareaSelRange.current
				startPos = Math.min(range.start, range.end);
				endPos = Math.max(range.start, range.end)
			}
			const pastedText = e.clipboardData.getData("text")
			// Check if the pasted content is a URL, add space after so user can easily delete if they don't want it
			const urlRegex = /^(http:\/\/|https:\/\/)\S+/
			if (urlRegex.test(pastedText.trim())) {
				e.preventDefault()
				const trimmedUrl = pastedText.trim()
				const selection = await mentionService.newUrlMentionSelection(trimmedUrl)
				let insertValue = trimmedUrl;
				if(selection.type === 'ICenter' && selection.title) {
					insertValue = selection.title
				}
				if (startPos === 0 || inputValue[startPos - 1] !== '@') {
					insertValue = '@' + insertValue
				}
				const newValue =
					inputValue.slice(0, startPos) + insertValue + " " + inputValue.slice(endPos)
				updateInputValue(newValue)
				const newCursorPosition = startPos + insertValue.length + 1
				setCursorPosition(newCursorPosition)
				setIntendedCursorPosition(newCursorPosition)
				setShowContextMenu(false)

				setSelections?.(mentionService.addItemToSelectedFile(selections, selection))
				// Scroll to new cursor position
				setTimeout(() => {
					if (textAreaRef.current) {
						textAreaRef.current.blur()
						textAreaRef.current.focus()
					}
				}, 0)

				return
			}
		},
		[cursorPosition, setInputValue, inputValue, setSelections, selections, textareaSelRange],
	)

	const updateCursorPosition = useCallback(() => {
		if (textAreaRef.current) {

			textareaSelRange.current = {
				start: textAreaRef.current.selectionStart,
				end: textAreaRef.current.selectionEnd,
			}

			setCursorPosition(textAreaRef.current.selectionStart)
		}
	}, [])

	const updateInputValue = useCallback((value: string) => {
		if (textAreaRef.current) {
			textAreaRef.current.value = value
			setInputValue(value)
			onChangeText?.(value)
		}
	}, [])

	const handleRemoveSelection=useCallback((removedMention: string) => {
		if (!selections) return;
		setSelections?.(selections.filter(selection => {
			if(!selection.fromMention) {
				return true;
			}
			// 处理对于没有fileURI的selection（如Codebase类型）
			const removedMentionValue = removedMention.substring(1)
			if(!selection.fileURI) {
				return selection.type !== removedMentionValue
			}

			// 获取文件路径的最后一级
			const lastSegment = selection.title;

			// 保留不匹配的元素
			return lastSegment !== removedMentionValue;
		}));
	},[selections, setSelections])

	const handleMenuMouseDown = useCallback(() => {
		setIsMouseDownOnMenu(true)
	}, [])

	const handleKeyDown = useCallback(
		(event: React.KeyboardEvent<HTMLTextAreaElement>) => {
			if (showContextMenu) {
				if (event.key === "Escape") {
					setSelectedType(null)
					setMouseSelectedIndex(0)
					setArrowSelectedIndex(0)
					setShowContextMenu(false)
					return
				}

				if (event.key === "ArrowUp" || event.key === "ArrowDown") {
					event.preventDefault()
					setArrowSelectedIndex((prevIndex) => {
						const direction = event.key === "ArrowUp" ? -1 : 1
						const options = getContextMenuOptions(
							searchQuery,
							queryItems
						)
						const optionsLength = options.length

						if (optionsLength === 0) return prevIndex

						// Find selectable options (non-URL types)
						const selectableOptions = options.filter(
							(option) =>
								option.type !== ContextMenuOptionType.NoResults,
						)

						if (selectableOptions.length === 0) return -1 // No selectable options

						// Find the index of the next selectable option
						const currentSelectableIndex = selectableOptions.findIndex(
							(option) => option === options[prevIndex],
						)

						const newSelectableIndex =
							(currentSelectableIndex + direction + selectableOptions.length) %
							selectableOptions.length

						// Find the index of the selected option in the original options array
						return options.findIndex((option) => option === selectableOptions[newSelectableIndex])
					})
					return
				}

				if ((event.key === "Enter" || event.key === "Tab") && arrowSelectedIndex !== -1) {
					event.preventDefault()
					const selectedOption = getContextMenuOptions(
						searchQuery,
						queryItems
					)[arrowSelectedIndex]
					if (
						selectedOption &&
						selectedOption.type !== ContextMenuOptionType.NoResults
					) {
						handleMentionSelect(selectedOption)
					}
					return
				}
			}

			const isComposing = event.nativeEvent?.isComposing ?? false
			if (event.key === "Enter" && !event.shiftKey && !isComposing) {
				event.preventDefault()
				setShowContextMenu(false)
				onKeyDown?.(event)
			}
			if (event.key === "Backspace" && !isComposing) {
				const cursorPos = cursorPosition;
				// 检查光标前的字符是否为空格
				if (cursorPos > 0 && inputValue[cursorPos - 1] === ' ') {
					const textBeforeSpace = inputValue.substring(0, cursorPos - 1);
					// 检查空格前是否有@mention
					const match = textBeforeSpace.match(new RegExp(mentionRegex.source + '$'));
					if (match) {
						// 找到了@mention，删除整个@mention和空格
						event.preventDefault();
						const mentionStart = cursorPos - 1 - match[0].length;
						const newValue = inputValue.substring(0, mentionStart) + inputValue.substring(cursorPos);
						updateInputValue(newValue);
						const newCursorPosition = mentionStart;
						setCursorPosition(newCursorPosition);
						setIntendedCursorPosition(newCursorPosition);
						setJustDeletedSpaceAfterMention(true);

						// 如果是@文件或文件夹，则删除相应的选择
						const mentionText = match[0];
						if(newValue.indexOf(mentionText) < 0) {
							handleRemoveSelection(mentionText);
						}

						return;
					}
				}
				setJustDeletedSpaceAfterMention(false);
			}
			else if (event.key === "ArrowLeft") {
				const charBeforeCursor = inputValue[cursorPosition - 1]
				if (charBeforeCursor === " ") {
					const beforeSpace = inputValue.slice(0, cursorPosition - 1)
					const matchBeforeSpace = beforeSpace.match(new RegExp(mentionRegex.source + "$"))
					if (matchBeforeSpace) {
						const mentionStart = cursorPosition - 1 - matchBeforeSpace[0].length
						event.preventDefault()
						textAreaRef.current?.setSelectionRange(mentionStart, mentionStart)
						setCursorPosition(mentionStart)
					}
				}
			}
			else if (event.key === "ArrowRight") {
				const textAfterCursor = inputValue.slice(cursorPosition)

				if (textAfterCursor.startsWith('@')) {
					const match = textAfterCursor.match(mentionRegex)
					if (match && match.index === 0) {
						const mentionEnd = cursorPosition + match[0].length
						const charAfterMention = inputValue[mentionEnd]

						if (charAfterMention === ' ') {
							const newPosition = mentionEnd + 1
							event.preventDefault()
							textAreaRef.current?.setSelectionRange(newPosition, newPosition)
							setCursorPosition(newPosition)
						} else {
							event.preventDefault()
							textAreaRef.current?.setSelectionRange(mentionEnd, mentionEnd)
							setCursorPosition(mentionEnd)
						}
					}
				}
			}
		},
		[
			showContextMenu,
			searchQuery,
			arrowSelectedIndex,
			handleMentionSelect,
			selectedType,
			inputValue,
			cursorPosition,
			setInputValue,
			justDeletedSpaceAfterMention,
			queryItems,
			onKeyDown,
			multiline,
			handleRemoveSelection
		],
	)
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				contextMenuContainerRef.current &&
				!contextMenuContainerRef.current.contains(event.target as Node)
			) {
				setShowContextMenu(false)
			}
		}

		if (showContextMenu) {
			document.addEventListener("mousedown", handleClickOutside)
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside)
		}
	}, [showContextMenu, setShowContextMenu])

	const listenTextAreaRectChange = useCallback((r: HTMLTextAreaElement) => {

		// 使用 ResizeObserver 监听节点的大小变化
		const resizeObserver = new ResizeObserver(() => {
			setTextAreaRect(r.getBoundingClientRect())
		});
		if (r) {
			resizeObserver.observe(r);
		}
		// 使用 MutationObserver 监听节点的变化
		const mutationObserver = new MutationObserver(() => {
			setTextAreaRect(r?.getBoundingClientRect())
		});
		if (r) {
			mutationObserver.observe(r, { attributes: true, childList: true, subtree: true });
		}
		// 清理观察者
		return () => {
			if (r) {
				resizeObserver.unobserve(r);
				mutationObserver.disconnect();
			}
		};
	},[])

	const updateHighlights = useCallback(() => {
		if (!textAreaRef.current || !highlightLayerRef.current) return

		highlightLayerRef.current.innerHTML = highLightMentionsFormater(textAreaRef.current.value, selections ?? [], false)

		highlightLayerRef.current.scrollTop = textAreaRef.current.scrollTop
		highlightLayerRef.current.scrollLeft = textAreaRef.current.scrollLeft
	}, [selections])

	useLayoutEffect(() => {
		updateHighlights()
	}, [updateHighlights, textAreaRef.current?.value])

	const handleFocusOnTextArea = useCallback(() => {
		if (textAreaRef.current) {
			textAreaRef.current.focus(); // 使 textarea 获得焦点
		}
	},[textAreaRef])
	return <>
		{showContextMenu && (
			<div ref={contextMenuContainerRef}>
				<ContextMenu
					onSelect={handleMentionSelect}
					searchQuery={searchQuery}
					onMouseDown={handleMenuMouseDown}
					mouseSelectedIndex={mouseSelectedMenuIndex}
					setMouseSelectedIndex={setMouseSelectedIndex}
					arrowSelectedIndex={arrowSelectedIndex}
					setArrowSelectedIndex={setArrowSelectedIndex}
					selectedType={selectedType}
					queryItems={queryItems}
					textAreaRect={textAreaRect}
					setSelectedType={setSelectedType}
					focusOnTextArea={handleFocusOnTextArea}
				/>
			</div>
		)}
		<div className={"relative flex-1 flex flex-col-reverse min-h-0 overflow-hidden"}>
			<div
				ref={highlightLayerRef}
				className={`absolute inset-0 pointer-events-none whitespace-pre-wrap break-words text-transparent overflow-hidden font-[var(--vscode-font-family)] text-[var(--vscode-editor-font-size)] leading-[var(--vscode-editor-line-height)] p-[2px] pr-[8px] ${thumbnailsHeight > 0 ? 'mb-[' + (thumbnailsHeight + 16) + 'px]' : ''} z-[1]`}
			/>
			<textarea
				ref={useCallback((r: HTMLTextAreaElement | null) => {
					if (fnsRef)
						fnsRef.current = fns

					textAreaRef.current = r
					if (typeof ref === 'function') ref(r)
					else if (ref) ref.current = r
					adjustHeight()
					if(r) {
						listenTextAreaRectChange(r)
					}
				}, [fnsRef, fns, setEnabled, adjustHeight, ref])}

				onFocus={onFocus}
				onBlur={onBlur}
				onPaste={handlePaste}
				onSelect={updateCursorPosition}
				onMouseUp={updateCursorPosition}
				disabled={enabled ? !isEnabled : true}

				className={`w-full resize-none max-h-[500px] overflow-y-auto text-md text-codeseek-fg-1 placeholder:text-codeseek-fg-3
					bg-[var(--vscode-codeseek-chatBG)] text-[var(--vscode-codeseek-chatFG)]
					w-full outline-none box-border bg-transparent rounded-[2px]
					font-[var(--vscode-font-family)] text-[var(--vscode-editor-font-size)]
					leading-[var(--vscode-editor-line-height)] resize-none overflow-x-hidden
					overflow-y-auto border-none p-[2px] pr-[8px]
					${thumbnailsHeight > 0 ? 'mb-[' + (thumbnailsHeight + 16) + 'px]' : ''}
					flex-none z-[2] [scrollbar-width:none] ${className}`}

				onChange={(e) => {
					onChange(e)
					updateHighlights()
				}}
				onKeyDown={handleKeyDown}
				onScroll={() => updateHighlights()}

				rows={1}
				placeholder={placeholder}
			/>
		</div>
	</>

});

export const CodeseekInputBox = ({ onChangeText, onCreateInstance, inputBoxRef, placeholder, isPasswordField, multiline, className }: {
	onChangeText: (value: string) => void;
	styles?: Partial<IInputBoxStyles>,
	onCreateInstance?: (instance: InputBox) => void | IDisposable[];
	inputBoxRef?: { current: InputBox | null };
	placeholder: string;
	isPasswordField?: boolean;
	multiline: boolean;
	className?: string;
}) => {

	const accessor = useAccessor()

	const contextViewProvider = accessor.get('IContextViewService')
	return <WidgetComponent
		ctor={InputBox}
		className={`
			bg-codeseek-bg-1
			@@[&_::placeholder]:!codeseek-text-codeseek-fg-3
			${className}
		`}
		propsFn={useCallback((container) => [
			container,
			contextViewProvider,
			{
				inputBoxStyles: {
					...defaultInputBoxStyles,
					inputForeground: "var(--vscode-foreground)",
					inputBackground: 'transparent',
					// inputBorder: 'none',
				},
				placeholder,
				tooltip: '',
				type: isPasswordField ? 'password' : undefined,
				flexibleHeight: multiline,
				flexibleMaxHeight: 500,
				flexibleWidth: false,
			}
		] as const, [contextViewProvider, placeholder, multiline])}
		dispose={useCallback((instance: InputBox) => {
			instance.dispose()
			instance.element.remove()
		}, [])}
		onCreateInstance={useCallback((instance: InputBox) => {
			const disposables: IDisposable[] = []
			disposables.push(
				instance.onDidChange((newText) => onChangeText(newText))
			)
			if (onCreateInstance) {
				const ds = onCreateInstance(instance) ?? []
				disposables.push(...ds)
			}
			if (inputBoxRef)
				inputBoxRef.current = instance;

			return disposables
		}, [onChangeText, onCreateInstance, inputBoxRef])
		}
	/>
};




export const CodeseekSwitch = ({
	value,
	onChange,
	size = 'md',
	label,
	disabled = false,
}: {
	value: boolean;
	onChange: (value: boolean) => void;
	label?: string;
	disabled?: boolean;
	size?: 'xs' | 'sm' | 'sm+' | 'md';
}) => {
	return (
		<label className="inline-flex items-center cursor-pointer">
			<div
				onClick={() => !disabled && onChange(!value)}
				className={`
			relative inline-flex items-center rounded-full transition-colors duration-200 ease-in-out
			${value ? 'bg-green-500 dark:bg-white' : 'bg-gray-300 dark:bg-gray-700'}
			${disabled ? 'opacity-25' : ''}
			${size === 'xs' ? 'h-4 w-7' : ''}
			${size === 'sm' ? 'h-5 w-9' : ''}
			${size === 'sm+' ? 'h-5 w-10' : ''}
			${size === 'md' ? 'h-6 w-11' : ''}
		  `}
			>
				<span
					className={`
			  inline-block transform rounded-full bg-white dark:bg-gray-900 shadow transition-transform duration-200 ease-in-out
			  ${size === 'xs' ? 'h-2.5 w-2.5' : ''}
			  ${size === 'sm' ? 'h-3 w-3' : ''}
			  ${size === 'sm+' ? 'h-3.5 w-3.5' : ''}
			  ${size === 'md' ? 'h-4 w-4' : ''}
			  ${size === 'xs' ? (value ? 'translate-x-3.5' : 'translate-x-0.5') : ''}
			  ${size === 'sm' ? (value ? 'translate-x-5' : 'translate-x-1') : ''}
			  ${size === 'sm+' ? (value ? 'translate-x-6' : 'translate-x-1') : ''}
			  ${size === 'md' ? (value ? 'translate-x-6' : 'translate-x-1') : ''}
			`}
				/>
			</div>
			{label && (
				<span className={`
			ml-3 font-medium
			${size === 'xs' ? 'text-xs' : 'text-sm'}
		  `}>
					{label}
				</span>
			)}
		</label>
	);
};





export const CodeseekCheckBox = ({ label, value, onClick, className }: { label: string, value: boolean, onClick: (checked: boolean) => void, className?: string }) => {
	const divRef = useRef<HTMLDivElement | null>(null)
	const instanceRef = useRef<Checkbox | null>(null)

	useEffect(() => {
		if (!instanceRef.current) return
		instanceRef.current.checked = value
	}, [value])


	return <WidgetComponent
		className={className ?? ''}
		ctor={Checkbox}
		propsFn={useCallback((container: HTMLDivElement) => {
			divRef.current = container
			return [label, value, defaultCheckboxStyles] as const
		}, [label, value])}
		onCreateInstance={useCallback((instance: Checkbox) => {
			instanceRef.current = instance;
			divRef.current?.append(instance.domNode)
			const d = instance.onChange(() => onClick(instance.checked))
			return [d]
		}, [onClick])}
		dispose={useCallback((instance: Checkbox) => {
			instance.dispose()
			instance.domNode.remove()
		}, [])}

	/>

}

const OptionIcon = ({ iconClasses, option, style }: { iconClasses: Record<ChatMode, string>, option: ChatMode, style?: React.CSSProperties }) => {
	return iconClasses &&
		<div className={`mr-1 ${iconClasses[option as ChatMode] || ''}`}
			style={{
				fontSize: '11px',
				width: '11px',
				height: '11px',
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				color: 'inherit',
				marginTop: '2px',
				...style,
			}}
		>
		</div>
}

export const CodeseekCustomDropdownBox = <T extends any>({
	options,
	iconClasses,
	selectedOption,
	onChangeOption,
	getOptionDropdownName,
	getOptionDisplayName,
	getOptionsEqual,
	className,
	arrowTouchesText = true,
	matchInputWidth = false,
	gap = 0,
}: {
	options: T[];
	iconClasses?: Record<ChatMode, string>;
	selectedOption: T | undefined;
	onChangeOption: (newValue: T) => void;
	getOptionDropdownName: (option: T) => string;
	getOptionDisplayName: (option: T) => string;
	getOptionsEqual: (a: T, b: T) => boolean;
	className?: string;
	arrowTouchesText?: boolean;
	matchInputWidth?: boolean;
	gap?: number;
}) => {
	const [isOpen, setIsOpen] = useState(false);
	const measureRef = useRef<HTMLDivElement>(null);

	// Replace manual positioning with floating-ui
	const {
		x,
		y,
		strategy,
		refs,
		middlewareData,
		update
	} = useFloating({
		open: isOpen,
		onOpenChange: setIsOpen,
		placement: 'bottom-start',

		middleware: [
			offset(gap),
			flip({
				boundary: document.body,
				padding: 8
			}),
			shift({
				boundary: document.body,
				padding: 8,
			}),
			size({
				apply({ availableHeight, elements, rects }) {
					const maxHeight = Math.min(availableHeight)

					Object.assign(elements.floating.style, {
						maxHeight: `${maxHeight}px`,
						overflowY: 'auto',
						// Ensure the width isn't constrained by the parent
						width: `${Math.max(
							rects.reference.width,
							measureRef.current?.offsetWidth ?? 0
						)}px`
					});
				},
				padding: 8,
				// Use viewport as boundary instead of any parent element
				boundary: document.body,
			}),
		],
		whileElementsMounted: autoUpdate,
		strategy: 'fixed',
	});

	// if the selected option is null, set the selection to the 0th option
	useEffect(() => {
		if (options.length === 0) return
		if (selectedOption) return
		onChangeOption(options[0])
	}, [selectedOption, onChangeOption, options])

	// Handle clicks outside
	useEffect(() => {
		if (!isOpen) return;

		const handleClickOutside = (event: MouseEvent) => {
			const target = event.target as Node;
			const floating = refs.floating.current;
			const reference = refs.reference.current;

			// Check if reference is an HTML element before using contains
			const isReferenceHTMLElement = reference && 'contains' in reference;

			if (
				floating &&
				(!isReferenceHTMLElement || !reference.contains(target)) &&
				!floating.contains(target)
			) {
				setIsOpen(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => document.removeEventListener('mousedown', handleClickOutside);
	}, [isOpen, refs.floating, refs.reference]);

	if (!selectedOption)
		return null

	return (
		<div className={`inline-block relative ${className}`}>
			{/* Hidden measurement div */}
			<div
				ref={measureRef}
				className="opacity-0 pointer-events-none absolute -left-[999999px] -top-[999999px] flex flex-col"
				aria-hidden="true"
			>
				{options.map((option) => (
					<div key={getOptionDropdownName(option)} className="flex items-center whitespace-nowrap">
						{iconClasses && Object.keys(iconClasses).length > 0 ? (
							<div className="flex-shrink-0 mr-2" style={{ width: '25px'}} />
						) : (
							<div className="w-4" />
						)}
						<span className="px-3">{getOptionDropdownName(option)}</span>
					</div>
				))}
			</div>

			{/* Select Button */}
			<button
				type='button'
				ref={refs.setReference}
				className="flex items-center h-4 bg-transparent whitespace-nowrap hover:brightness-90 w-full text-current"
				onClick={() => setIsOpen(!isOpen)}
			>
				{iconClasses && <OptionIcon iconClasses={iconClasses} option={selectedOption as ChatMode} style={{ paddingTop: '1.5px' }} />}
				<span className={`max-w-[120px] pt-[1.5px] truncate ${arrowTouchesText ? 'mr-1' : ''}
					${iconClasses && iconClasses[selectedOption as ChatMode] ? 'text-codeseek-fg-1 font-medium' : ''}`}
				>
					{getOptionDisplayName(selectedOption)}
				</span>
				<svg
					className={`size-3 flex-shrink-0 ${arrowTouchesText ? '' : 'ml-auto'}`}
					viewBox="0 0 12 12"
					fill="none"
				>
					<path
						d="M2.5 4.5L6 8L9.5 4.5"
						stroke="currentColor"
						strokeWidth="1.5"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
				</svg>
			</button>

			{/* Dropdown Menu */}
			{isOpen && (
				<div
					ref={refs.setFloating}
					className="z-10 bg-codeseek-bg-1 border-codeseek-border-1 border overflow-hidden rounded shadow-lg"
					style={{
						position: strategy,
						top: y ?? 0,
						left: x ?? 0,
						width: matchInputWidth
							? (refs.reference.current instanceof HTMLElement ? refs.reference.current.offsetWidth : 0)
							: Math.max(
								(refs.reference.current instanceof HTMLElement ? refs.reference.current.offsetWidth : 0),
								(measureRef.current instanceof HTMLElement ? measureRef.current.offsetWidth : 0)
							),
					}}
				>
					{options.map((option) => {
						const thisOptionIsSelected = getOptionsEqual(option, selectedOption);
						const optionName = getOptionDropdownName(option);

						return (
							<div
								key={optionName}
								className={`flex items-center px-2 py-0.5 cursor-pointer whitespace-nowrap
									transition-all duration-100
									bg-codeseek-bg-2
									${thisOptionIsSelected ? 'bg-codeseek-bg-1' : 'hover:bg-codeseek-bg-5 hover:text-codeseek-fg-1'}
								`}
								onClick={() => {
									// if (option === ChatMode.Agent) {
									// 	return;
									// }
									onChangeOption(option);
									setIsOpen(false);
								}}
							>
								{iconClasses && <OptionIcon iconClasses={iconClasses} option={option as ChatMode} />}
								<span className="overflow-hidden text-ellipsis">{optionName}</span>
								<div className="w-4 flex justify-center flex-shrink-0 ml-auto">
									{thisOptionIsSelected && (
										<svg className="size-3" viewBox="0 0 12 12" fill="none">
											<path
												d="M10 3L4.5 8.5L2 6"
												stroke="currentColor"
												strokeWidth="1.5"
												strokeLinecap="round"
												strokeLinejoin="round"
											/>
										</svg>
									)}
								</div>
							</div>
						);
					})}
				</div>
			)}
		</div>
	);
};



export const _CodeseekSelectBox = <T,>({ onChangeSelection, onCreateInstance, selectBoxRef, options, className }: {
	onChangeSelection: (value: T) => void;
	onCreateInstance?: ((instance: SelectBox) => void | IDisposable[]);
	selectBoxRef?: React.MutableRefObject<SelectBox | null>;
	options: readonly { text: string, value: T }[];
	className?: string;
}) => {
	const accessor = useAccessor()
	const contextViewProvider = accessor.get('IContextViewService')

	let containerRef = useRef<HTMLDivElement | null>(null);

	return <WidgetComponent
		className={`
			@@select-child-restyle
			@@[&_select]:!codeseek-text-codeseek-fg-3
			@@[&_select]:!codeseek-text-xs
			!text-codeseek-fg-3
			${className ?? ''}
		`}
		ctor={SelectBox}
		propsFn={useCallback((container) => {
			containerRef.current = container
			const defaultIndex = 0;
			return [
				options.map(opt => ({ text: opt.text })),
				defaultIndex,
				contextViewProvider,
				defaultSelectBoxStyles,
			] as const;
		}, [containerRef, options])}

		dispose={useCallback((instance: SelectBox) => {
			instance.dispose();
			containerRef.current?.childNodes.forEach(child => {
				containerRef.current?.removeChild(child)
			})
		}, [containerRef])}

		onCreateInstance={useCallback((instance: SelectBox) => {
			const disposables: IDisposable[] = []

			if (containerRef.current)
				instance.render(containerRef.current)

			disposables.push(
				instance.onDidSelect(e => { onChangeSelection(options[e.index].value); })
			)

			if (onCreateInstance) {
				const ds = onCreateInstance(instance) ?? []
				disposables.push(...ds)
			}
			if (selectBoxRef)
				selectBoxRef.current = instance;

			return disposables;
		}, [containerRef, onChangeSelection, options, onCreateInstance, selectBoxRef])}

	/>;
};

// makes it so that code in the sidebar isnt too tabbed out
const normalizeIndentation = (code: string): string => {
	const lines = code.split('\n')

	let minLeadingSpaces = Infinity

	// find the minimum number of leading spaces
	for (const line of lines) {
		if (line.trim() === '') continue;
		let leadingSpaces = 0;
		for (let i = 0; i < line.length; i++) {
			const char = line[i];
			if (char === '\t' || char === ' ') {
				leadingSpaces += 1;
			} else { break; }
		}
		minLeadingSpaces = Math.min(minLeadingSpaces, leadingSpaces)
	}

	// remove the leading spaces
	return lines.map(line => {
		if (line.trim() === '') return line;

		let spacesToRemove = minLeadingSpaces;
		let i = 0;
		while (spacesToRemove > 0 && i < line.length) {
			const char = line[i];
			if (char === '\t' || char === ' ') {
				spacesToRemove -= 1;
				i++;
			} else { break; }
		}

		return line.slice(i);

	}).join('\n')

}


const modelOfEditorId: { [id: string]: ITextModel | undefined } = {}
export type CodeseekCodeEditorProps = { initValue: string, language?: string, maxHeight?: number, showScrollbars?: boolean }
export const CodeseekCodeEditor = ({ initValue, language, maxHeight, showScrollbars }: CodeseekCodeEditorProps) => {

	initValue = normalizeIndentation(initValue)

	// default settings
	const MAX_HEIGHT = maxHeight ?? Infinity;
	const SHOW_SCROLLBARS = showScrollbars ?? false;

	const divRef = useRef<HTMLDivElement | null>(null)

	const accessor = useAccessor()
	const instantiationService = accessor.get('IInstantiationService')
	// const languageDetectionService = accessor.get('ILanguageDetectionService')
	const modelService = accessor.get('IModelService')


	const id = useId()

	// these are used to pass to the model creation of modelRef
	const initValueRef = useRef(initValue)
	const languageRef = useRef(language)

	const modelRef = useRef<ITextModel | null>(null)

	// if we change the initial value, don't re-render the whole thing, just set it here. same for language
	useEffect(() => {
		initValueRef.current = initValue
		modelRef.current?.setValue(initValue)
	}, [initValue])
	useEffect(() => {
		languageRef.current = language
		if (language) modelRef.current?.setLanguage(language)
	}, [language])

	return <div ref={divRef} className='relative z-0 px-2 py-1 bg-codeseek-bg-3'>
		<WidgetComponent
			className='@@bg-editor-style-override' // text-sm
			ctor={useCallback((container) => {
				return instantiationService.createInstance(
					CodeEditorWidget,
					container,
					{
						automaticLayout: true,
						wordWrap: 'off',
						fontSize: 12,

						scrollbar: {
							alwaysConsumeMouseWheel: false,
							...SHOW_SCROLLBARS ? {
								vertical: 'auto',
								verticalScrollbarSize: 8,
								horizontal: 'auto',
								horizontalScrollbarSize: 8,
							} : {
								vertical: 'hidden',
								verticalScrollbarSize: 0,
								horizontal: 'auto',
								horizontalScrollbarSize: 8,
								ignoreHorizontalScrollbarInContentHeight: true,

							},
						},
						scrollBeyondLastLine: false,

						lineNumbers: 'off',

						readOnly: true,
						domReadOnly: true,
						readOnlyMessage: { value: '' },

						minimap: {
							enabled: false,
							// maxColumn: 0,
						},

						hover: { enabled: false },

						selectionHighlight: false, // highlights whole words
						renderLineHighlight: 'none',

						folding: false,
						lineDecorationsWidth: 0,
						overviewRulerLanes: 0,
						hideCursorInOverviewRuler: true,
						overviewRulerBorder: false,
						glyphMargin: false,

						stickyScroll: {
							enabled: false,
						},
					},
					{
						isSimpleWidget: true,
					})
			}, [instantiationService])}

			onCreateInstance={useCallback((editor: CodeEditorWidget) => {
				const model = modelOfEditorId[id] ?? modelService.createModel(
					initValueRef.current + '\n', {
					languageId: languageRef.current ? languageRef.current : 'typescript',
					onDidChange: (e) => { return { dispose: () => { } } } // no idea why they'd require this
				})
				modelRef.current = model
				editor.setModel(model);

				const container = editor.getDomNode()
				const parentNode = container?.parentElement
				const resize = () => {
					const height = editor.getScrollHeight() + 1
					if (parentNode) {
						// const height = Math.min(, MAX_HEIGHT);
						parentNode.style.height = `${height}px`;
						parentNode.style.maxHeight = `${MAX_HEIGHT}px`;
						editor.layout();
					}
				}

				resize()
				const disposable = editor.onDidContentSizeChange(() => { resize() });

				return [disposable, model]
			}, [modelService])}

			dispose={useCallback((editor: CodeEditorWidget) => {
				editor.dispose();
			}, [modelService])}

			propsFn={useCallback(() => { return [] }, [])}
		/>
	</div>

}


export const CodeseekButton = ({ className, appearance, children, disabled, style, title, onClick, onMouseEnter, onMouseLeave, onMouseDown, onMouseUp }: {
	className?: string;
	appearance?: 'primary' | 'secondary';
	children?: React.ReactNode;
	disabled?: boolean;
	style?: React.CSSProperties;
	title?: string;
	onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
	onMouseEnter?: (e: React.MouseEvent<HTMLButtonElement>) => void;
	onMouseLeave?: (e: React.MouseEvent<HTMLButtonElement>) => void;
	onMouseDown?: (e: React.MouseEvent<HTMLButtonElement>) => void;
	onMouseUp?: (e: React.MouseEvent<HTMLButtonElement>) => void;
}) => {
	return <button disabled={disabled}
		className={`px-3 py-1 bg-black/10 dark:bg-gray-200/10 rounded-sm overflow-hidden
			${appearance === 'primary' ? 'bg-codeseek-bg-1' : ''} ${className} ${disabled ? 'disabled' : ''}
		`}
		style={style}
		onClick={onClick}
		title={title}
		onMouseEnter={onMouseEnter}
		onMouseLeave={onMouseLeave}
		onMouseDown={onMouseDown}
		onMouseUp={onMouseUp}
	>{children || ''}</button>
}

export const CodeseekButton2 = ({ className, children, onClick}: {
	className?: string;
	children?: React.ReactNode;
	onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
}) => {
	return <span className={`text-codeseek-fg-3 hover:text-codeseek-fg-1 hover:bg-codeseek-bg-5 rounded-sm ${className}`}
		onClick={onClick}
	>{children || ''}</span>
}

export const SettingButton = ({ className, children, disabled, style, title, onClick }: {
	className?: string;
	children?: React.ReactNode;
	disabled?: boolean;
	style?: React.CSSProperties;
	title?: string;
	onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
}) => {
	const className_ = className ? `${className}` : 'bg-blue-500 hover:bg-blue-600 dark:bg-gray-200/10 dark:hover:bg-gray-200/5 text-white'
	return <button disabled={disabled}
		className={`px-3 pt-1 pb-1.5 rounded-sm overflow-hidden ${className_} ${disabled ? 'disabled'  : '' }
		`}
		style={style}
		onClick={onClick}
		title={title}
	>{children || ''}</button>
}

// export const CodeseekScrollableElt = ({ options, children }: { options: ScrollableElementCreationOptions, children: React.ReactNode }) => {
// 	const instanceRef = useRef<DomScrollableElement | null>(null);
// 	const [childrenPortal, setChildrenPortal] = useState<React.ReactNode | null>(null)

// 	return <>
// 		<WidgetComponent
// 			ctor={DomScrollableElement}
// 			propsFn={useCallback((container) => {
// 				return [container, options] as const;
// 			}, [options])}
// 			onCreateInstance={useCallback((instance: DomScrollableElement) => {
// 				instanceRef.current = instance;
// 				setChildrenPortal(createPortal(children, instance.getDomNode()))
// 				return []
// 			}, [setChildrenPortal, children])}
// 			dispose={useCallback((instance: DomScrollableElement) => {
// 				console.log('calling dispose!!!!')
// 				// instance.dispose();
// 				// instance.getDomNode().remove()
// 			}, [])}
// 		>{children}</WidgetComponent>

// 		{childrenPortal}

// 	</>
// }

// export const CodeseekSelectBox = <T,>({ onChangeSelection, initVal, selectBoxRef, options }: {
// 	initVal: T;
// 	selectBoxRef: React.MutableRefObject<SelectBox | null>;
// 	options: readonly { text: string, value: T }[];
// 	onChangeSelection: (value: T) => void;
// }) => {


// 	return <WidgetComponent
// 		ctor={DropdownMenu}
// 		propsFn={useCallback((container) => {
// 			return [
// 				container, {
// 					contextMenuProvider,
// 					actions: options.map(({ text, value }, i) => ({
// 						id: i + '',
// 						label: text,
// 						tooltip: text,
// 						class: undefined,
// 						enabled: true,
// 						run: () => {
// 							onChangeSelection(value);
// 						},
// 					}))

// 				}] as const;
// 		}, [options, initVal, contextViewProvider])}

// 		dispose={useCallback((instance: DropdownMenu) => {
// 			instance.dispose();
// 			// instance.element.remove()
// 		}, [])}

// 		onCreateInstance={useCallback((instance: DropdownMenu) => {
// 			return []
// 		}, [])}

// 	/>;
// };




// export const CodeseekCheckBox = ({ onChangeChecked, initVal, label, checkboxRef, }: {
// 	onChangeChecked: (checked: boolean) => void;
// 	initVal: boolean;
// 	checkboxRef: React.MutableRefObject<ObjectSettingCheckboxWidget | null>;
// 	label: string;
// }) => {
// 	const containerRef = useRef<HTMLDivElement>(null);


// 	useEffect(() => {
// 		if (!containerRef.current) return;

// 		// Create and mount the Checkbox using VSCode's implementation

// 		checkboxRef.current = new ObjectSettingCheckboxWidget(
// 			containerRef.current,
// 			themeService,
// 			contextViewService,
// 			hoverService,
// 		);


// 		checkboxRef.current.setValue([{
// 			key: { type: 'string', data: label },
// 			value: { type: 'boolean', data: initVal },
// 			removable: false,
// 			resetable: true,
// 		}])

// 		checkboxRef.current.onDidChangeList((list) => {
// 			onChangeChecked(!!list);
// 		})


// 		// cleanup
// 		return () => {
// 			if (checkboxRef.current) {
// 				checkboxRef.current.dispose();
// 				if (containerRef.current) {
// 					while (containerRef.current.firstChild) {
// 						containerRef.current.removeChild(containerRef.current.firstChild);
// 					}
// 				}
// 				checkboxRef.current = null;
// 			}
// 		};
// 	}, [checkboxRef, label, initVal, onChangeChecked]);

// 	return <div ref={containerRef} className="w-full" />;
// };
