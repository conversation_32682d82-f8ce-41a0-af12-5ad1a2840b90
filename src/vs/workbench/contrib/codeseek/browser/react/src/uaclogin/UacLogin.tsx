/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useAccessor, useIsDark }  from '../util/services.js'
import { useScrollbarStyles } from '../util/useScrollbarStyles.js'
import { QRCodeCanvas } from 'qrcode.react';
import { md5QrCode, verfiyCode} from '../../../../common/uac/qrCode.js'
import { AuthInfo } from '../../../../common/uac/UacloginTypes.js'
import { CancellationTokenSource } from '../../../../../../../base/common/cancellation.js';
import { UserInfo } from '../../../../common/uac/UacloginTypes.js';



enum TabName {
	SCAN_QR = '扫码登陆',
	ACCOUNT = '账号登陆',
	USER_INFO = '个人信息'
}

type QrCodeTabProps = {
	genQrCode: () => void;
	qrCodeValue:string | undefined;
	check: () => Promise<string[]>;
}


export const UacLoginButton = ({ className, children, disabled, style, title, onClick }: {
	className?: string;
	children?: React.ReactNode;
	disabled?: boolean;
	style?: React.CSSProperties;
	title?: string;
	onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
}) => {
	return <button disabled={disabled}
		className={`px-3 pt-1 pb-1.5 bg-blue-500 hover:bg-blue-600 dark:bg-gray-200/10 dark:hover:bg-gray-200/5 rounded-sm overflow-hidden text-white
			${className} ${disabled ? 'disabled' : ''}
		`}
		style={style}
		onClick={onClick}
		title={title}
	>{children || ''}</button>
}

export const QrCodeTab:React.FC<QrCodeTabProps> = ({qrCodeValue, genQrCode, check}) => {

	const [prombles, setPrombles] = useState<string[]>([])
	const handleCheck = () =>{
		check().then(res => {
			setPrombles(res)
		})
	}

	const feedLink = "https://i.zte.com.cn/#/space/4b01feaa64cc4fa7b112fe5544bdd95e/wiki/page/5ad95b293ed24376a4225f5a1b345f77/view"
	const invalidQrUri = "https://uac.zte.com.cn/portal/app/loginPage/images/invalid.png"
	return <>
		<section className="component-container">
          <section className="component-item">
            <section>
              {qrCodeValue ? (
				<p id="valid-qr">
					<span className="p-5 w-[150px] h-[150px] bg-white block flex items-center justify-center">
						<QRCodeCanvas value={qrCodeValue} size={130} />
					</span>
				</p>
			  ) : (
				<p id="invalid-qr">
					<span className="p-[10px]  w-[150px] h-[150px] bg-gray-400 block flex items-center justify-center">
						<img src={invalidQrUri} alt="loading..." onClick={genQrCode}/>
					</span>
					<span className="error-text">二维码已失效，请点击图片重新生成</span>
				</p>
			  )}
            </section>
          </section>
          <section className="component-item">
            <section>
              <UacLoginButton onClick={handleCheck}>有问题？点我检测</UacLoginButton>
              {/* <vscode-progress-ring id="check-progrss-ring" class="hidden"></vscode-progress-ring> */}
              <section id="check-results-container">
			  { prombles.includes("no-error") && <p id="no-error">未检测到任何问题，<a id="feed-btn" target="_blank" href={feedLink}>点我反馈</a></p>}
                { prombles.includes("proxy-error") && <p id="proxy-error" className="warn-text">有配置代理网络，可能引起网络异常!</p> }
				{ prombles.includes("inter-network-error") &&  <p id="inter-network-error" className="error-text">公司内网不通!</p>}
                { prombles.includes("uac-error") && <p id="uac-error" className="error-text">UAC服务不通!</p>}
				{ prombles.includes("icenter-error") && <p id="icenter-error" className="error-text">iCenter服务不通!</p>}
				{ prombles.includes("rdc-error") && <p id="rdc-error" className="error-text">RDC服务不通!</p>}
              </section>
            </section>
          </section>
        </section>
	</>
}



const AccountTab:React.FC<{login:(authInfo: AuthInfo) => void }> = ({login}) => {
	const isInputValid = useCallback((authInfo: AuthInfo) => {
		const userIdReg = /^\d{8}$/;
		return (authInfo.userId && authInfo.password && authInfo.dynPwd && userIdReg.test(authInfo.userId));
	}, [])

	const [userId, setUserId] = useState('');
	const [password, setPassword] = useState('');
	const [dynPwd, setDynPwd] = useState('');
	const [errorInfo, setErrorInfo]  = useState('')

	const handleLogin = useCallback(() => {
		const authInfo: AuthInfo = { userId, password, dynPwd }; // 从状态中获取值

		if (isInputValid(authInfo)) {
			setErrorInfo('')
			login(authInfo)
		} else {
			setErrorInfo("输入信息格式有误，请检查！");
		}
	}, [isInputValid, userId, password, dynPwd])

	return <>
		<section className="component-container">
			<section className="component-item flex items-center">
				<span className="codicon codicon-account mr-1"></span>
				<input
					value={userId}
					onChange={(e) => setUserId(e.target.value)}
					placeholder="请输入８位工号"
					className='border border-codeseek-border-3 winthin:border-codeseek-border-1
					text-codeseek-fg-1 placeholder:text-codeseek-fg-3 bg-[var(--vscode-codeseek-chatBG)]
					text-[var(--vscode-codeseek-chatFG)] p-[2px]'
				/>
			</section>
			<section className="component-item flex items-center">
			<span className="codicon codicon-eye-closed mr-1"></span>
				<input
					value={password}
					onChange={(e) => setPassword(e.target.value)}
					placeholder="请输入人事密码"
					type="password"
					className='border border-codeseek-border-3 winthin:border-codeseek-border-1
					text-codeseek-fg-1 placeholder:text-codeseek-fg-3 bg-[var(--vscode-codeseek-chatBG)]
					text-[var(--vscode-codeseek-chatFG)] p-[2px]'
				/>
			</section>
			<section className="component-item flex items-center">
				<span className="codicon codicon-watch mr-1"></span>
				<input
					value={dynPwd}
					onChange={(e) => setDynPwd(e.target.value)}
					placeholder="请输入动态口令"
					className='border border-codeseek-border-3 winthin:border-codeseek-border-1
					text-codeseek-fg-1 placeholder:text-codeseek-fg-3 bg-[var(--vscode-codeseek-chatBG)]
					text-[var(--vscode-codeseek-chatFG)] p-[2px]'
				/>
			</section>
			<section className="component-item">
				<UacLoginButton onClick={handleLogin}>登录</UacLoginButton>
				<p id="error-info" className="error-text">{errorInfo}</p>
			</section>
		</section>
	</>
}

type UserInfoTabProps = {
    userInfo: UserInfo | undefined;
	logout:() =>void;
	copyToClipboard: (value: string) => void;
};
const UserInfoTab: React.FC<UserInfoTabProps> = ({ userInfo, logout, copyToClipboard }) => {

	function copyHttpPassword() {
		if(userInfo && userInfo.gerritHttpPassword){
			copyToClipboard(userInfo.gerritHttpPassword);
		}
	}

	return <>
		<section className="component-container">
          <section className="component-item">
            <label className='pr-1'>姓名: </label>
            <span id="username">{userInfo?.username}</span>
          </section>
          <section className="component-item">
            <label className='pr-1'>部门: </label>
            <span id="department">{userInfo?.department}</span>
          </section>
          <section className="component-item">
            <label className='pr-1'>HTTP密码（gerrit）: </label>
			<input value={userInfo?.gerritHttpPassword?.substring(0, 11) || ''} readOnly type="password" className='bg-[var(--vscode-codeseek-chatBG)]' />
			<button id="copy-http-password" slot="end" className="codicon codicon-copy" onClick={copyHttpPassword}></button>
          </section>
          <section className="component-item">
			<UacLoginButton disabled={!userInfo} onClick={logout}>注销</UacLoginButton>
          </section>
        </section>
	</>
}

export const UacLogin = () => {
	const isDark = useIsDark()

	const accessor = useAccessor()
	const codeseekUacLoginService = accessor.get('ICodeseekUacLoginService')
	const clipboardService = accessor.get('IClipboardService');
	const notificationService = accessor.get('INotificationService');

	const [userInfo, setUserInfo] = useState<UserInfo | undefined>(undefined)

	const [tab, setTab] = useState<TabName>(TabName.SCAN_QR)

	const containerRef = useRef<HTMLDivElement | null>(null)
	useScrollbarStyles(containerRef)


	const abortControllerRef = useRef<CancellationTokenSource | null>(null);
	codeseekUacLoginService.onDidChangeUserInfo((res) => {
		setUserInfo(res)
	})

	codeseekUacLoginService.onDidUserLogout(() => {
		setTab(TabName.SCAN_QR)
		setUserInfo(undefined)
	})
	const qrLogin = useCallback((dataStr:string) => {
		const tokenSource = new CancellationTokenSource();
		abortControllerRef.current = tokenSource;
		return codeseekUacLoginService.qrLogin(dataStr, tokenSource.token);
	},[codeseekUacLoginService])
	const login = useCallback((authInfo:AuthInfo) => {
		codeseekUacLoginService.login(authInfo).then(ret => {
			if(ret) {
				setTab(TabName.USER_INFO)
				setQrCodeValue(undefined)
			}
		})
	},[])

	const [qrCodeValue, setQrCodeValue] = useState<string | undefined>(undefined)
	const genQrCode = useCallback(() => {
		const code = md5QrCode('TwoDIMAuth');   //获取二维码串

		const codeArr = code.split(":");  //二维码串用冒号分割
		const qrCodeKey = codeArr[1];     //二维码串的key值
		const qrCodeValue = codeArr[2];   //二维码串的value值
		const ip = '127.0.0.1';
		const originSystemCode = "";
		const loginSystemCode = "100000430854";
		const dataStr = '{"qrCodeKey":"' + qrCodeKey + '","qrCodeValue":"' + qrCodeValue + '","loginClientIp":"' + ip + '","originSystemCode":"' + originSystemCode + '","loginSystemCode":"' + loginSystemCode + '","verifyCode":"' + verfiyCode(qrCodeKey+qrCodeValue+ip+loginSystemCode+originSystemCode)  + '"}';

		qrLogin(dataStr).then(() => {
			setTab(TabName.USER_INFO)
			setQrCodeValue(undefined)
		})
		setQrCodeValue(code)
	}, [qrLogin, setTab])

	const [hasInitialized, setHasInitialized] = useState(false);

	useEffect(() => {
		if(userInfo) {
			setTab(TabName.USER_INFO)
			setQrCodeValue(undefined)
			if (abortControllerRef.current) {
				abortControllerRef.current.cancel();
				abortControllerRef.current.dispose();
			}
		}
	},[userInfo])
	useEffect(() => {
		if (!hasInitialized) {
			codeseekUacLoginService.getUserInfo().then(newUserInfo => {
				setUserInfo(newUserInfo)
				if(newUserInfo) {
					setTab(TabName.USER_INFO)
				} else {
					if (document.cookie) {
						// code-server
						codeseekUacLoginService.codeServerLogin(document.cookie);
					} else {
						// vscode
						codeseekUacLoginService.udsLogin();
					}
					genQrCode()
				}
				setHasInitialized(true);
			})
		}
	}, [userInfo, hasInitialized]);

	const handleLogout = useCallback(()=>{
		codeseekUacLoginService.logout().finally(() => {
			setTab(TabName.SCAN_QR)
			setQrCodeValue(undefined)
		});
	},[])

	const check = useCallback(() => {
		return codeseekUacLoginService.check()
	},[])

	const copyToClipboard = useCallback((value: string) => {
			clipboardService.writeText(value).then(() => {
				notificationService.info("已复制！");
			}).catch((e) => {
				notificationService.error(e.message)
			});
	},[])
	return <div className={`@@codeseek-scope ${isDark ? 'dark' : ''}`} style={{ height: '100%', width: '100%' }}>
		<div ref={containerRef} className='overflow-y-auto w-full h-full px-1 select-none'>
			<div className='w-full h-full'>
				<h1 className='text-2xl w-full px-2 py-4 border-b border-codeseek-border-3'>{`ZTE用户统一认证`}</h1>

				<div className='flex items-stretch h-full mx-1'>
					{/* tabs */}
					<div className='flex flex-col w-full max-w-32 border-r border-codeseek-border-3'>
						<button className={`flex items-center text-left p-1 mr-1 my-0.5 rounded-sm overflow-hidden ${tab === TabName.SCAN_QR ? 'bg-black/10 dark:bg-gray-200/10' : ''} hover:bg-black/10 hover:dark:bg-gray-200/10 active:bg-black/10 active:dark:bg-gray-200/10 `}
							onClick={() => { setTab(TabName.SCAN_QR) }}
						>
							<span className='flex items-center codicon px-2'/>
							<span>扫码登录</span>
						</button>
						<button className={`flex items-center text-left p-1 mr-1 my-0.5 rounded-sm overflow-hidden ${tab === TabName.ACCOUNT ? 'bg-black/10 dark:bg-gray-200/10' : ''} hover:bg-black/10 hover:dark:bg-gray-200/10 active:bg-black/10 active:dark:bg-gray-200/10 `}
							onClick={() => { setTab(TabName.ACCOUNT) }}
						>
							<span className='flex items-center codicon  px-2'/>
							<span>账号登陆</span>
						</button>

						<button className={`flex items-center text-left p-1 mr-1 my-0.5 rounded-sm overflow-hidden ${tab === TabName.USER_INFO ? 'bg-black/10 dark:bg-gray-200/10' : ''} hover:bg-black/10 hover:dark:bg-gray-200/10 active:bg-black/10 active:dark:bg-gray-200/10 `}
							onClick={() => { setTab(TabName.USER_INFO) }}
						>
							<span className='flex items-center codicon px-2'/>
							<span>个人信息</span>
						</button>
					</div>

					{/* content */}
					<div className='flex-1 overflow-auto mx-4 mt-3'>
						<div className={`${tab !== TabName.SCAN_QR ? 'hidden' : ''}`}>
							<QrCodeTab genQrCode={genQrCode} qrCodeValue={qrCodeValue} check={check}/>
						</div>

						<div className={`${tab !== TabName.ACCOUNT ? 'hidden' : ''}`}>
							<AccountTab login={login}/>
						</div>
						<div className={`${tab !== TabName.USER_INFO ? 'hidden' : ''}`}>
							<UserInfoTab userInfo={userInfo} logout={handleLogout} copyToClipboard={copyToClipboard} />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
