/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { useCallback, useEffect, useRef, useState } from 'react'
import { FeatureName, isFeatureNameDisabled, ModelOption, modelSelectionsEqual } from '../../../../common/codeseekSettingsTypes.js'
import { useSettingsState, useAccessor } from '../util/services.js'
import { _CodeseekSelectBox, CodeseekCustomDropdownBox } from '../util/inputs.js'
import { CODESEEK_OPEN_SETTINGS_ACTION_ID } from '../../../codeseekSettingsPane.js'
import { WarningBox } from './WarningBox.js'

const optionsEqual = (m1: ModelOption[], m2: ModelOption[]) => {
	if (m1.length !== m2.length) return false
	for (let i = 0; i < m1.length; i++) {
		if (!modelSelectionsEqual(m1[i].selection, m2[i].selection)) return false
	}
	return true
}

const ModelSelectBox = ({ options, featureName, containerId }: { options: ModelOption[], featureName: FeatureName, containerId?: string }) => {
	const accessor = useAccessor()
	const codeseekSettingsService = accessor.get('ICodeseekSettingsService')

	const selection = codeseekSettingsService.getModelSelectionForContainer(featureName, containerId)

	const selectedOption = selection ? codeseekSettingsService.state._modelOptions.find(v => modelSelectionsEqual(v.selection, selection))! : options[0]

	const onChangeOption = useCallback((newOption: ModelOption) => {
		codeseekSettingsService.setModelSelectionOfFeature(featureName, newOption.selection, containerId)
	}, [codeseekSettingsService, featureName, containerId])

	return <CodeseekCustomDropdownBox
		options={options}
		selectedOption={selectedOption}
		onChangeOption={onChangeOption}
		getOptionDisplayName={(option) => option.selection.modelName}
		getOptionDropdownName={(option) => option.name}
		getOptionsEqual={(a, b) => optionsEqual([a], [b])}
		className='text-xs text-codeseek-fg-3 px-1'
		matchInputWidth={false}
	/>
}

const MemoizedModelDropdown = ({ featureName, containerId }: { featureName: FeatureName, containerId?: string }) => {
	const settingsState = useSettingsState()
	const oldOptionsRef = useRef<ModelOption[]>([])
	const [memoizedOptions, setMemoizedOptions] = useState(oldOptionsRef.current)

	useEffect(() => {
		const oldOptions = oldOptionsRef.current
		const newOptions = settingsState._modelOptions
		if (!optionsEqual(oldOptions, newOptions)) {
			setMemoizedOptions(newOptions)
		}
		oldOptionsRef.current = newOptions
	}, [settingsState._modelOptions])
	let filteredOptions = memoizedOptions
	if (featureName === 'Apply'){
		filteredOptions = memoizedOptions.filter(option => option.selection.isApplyModel || option.selection.providerName === 'openAICompatible')
	}else {
		filteredOptions = memoizedOptions.filter(option => !option.selection.isApplyModel)
	}
	return <ModelSelectBox featureName={featureName} options={filteredOptions} containerId={containerId} />

}

export const ModelDropdown = ({ featureName, containerId }: { featureName: FeatureName, containerId?: string }) => {
	const settingsState = useSettingsState()

	const accessor = useAccessor()
	const commandService = accessor.get('ICommandService')

	const openSettings = () => { commandService.executeCommand(CODESEEK_OPEN_SETTINGS_ACTION_ID); };

	const isDisabled = isFeatureNameDisabled(featureName, settingsState)
	if (isDisabled)
		return <WarningBox className='mt-[3px]' onClick={openSettings} text={
			isDisabled === 'needToEnableModel' ? 'Enable a model'
				: isDisabled === 'addModel' ? 'Add a model'
					: (isDisabled === 'addProvider' || isDisabled === 'notFilledIn' || isDisabled === 'providerNotAutoDetected') ? 'Provider required'
						: 'Provider required'
		} />

	return <MemoizedModelDropdown featureName={featureName} containerId={containerId} />
}
