/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

@tailwind base;
@tailwind components;
@tailwind utilities;


.select-child-restyle select {
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-right: 24px;
}

* {
	outline: none !important;
}


.inherit-bg-all-restyle > * {
	background-color: inherit !important;
}


.bg-editor-style-override {
	--vscode-sideBar-background: var(--vscode-editor-background);
}

.input-icon-button {
	cursor: pointer;
	opacity: 1;
}

.input-icon-button.disabled {
	cursor: pointer;
	opacity: 0.65;
}

.input-icon-button.disabled:hover {
	opacity: 1;
}

.select-file-icon-container {
	width: 18px;
	height: 18px;
	min-width: 18px;
	min-height: 18px;
}

.select-file-close-icon {
	scale: 0.78;
}

.select-file-icon {
	scale: 0.75;
}

.mention-context-textarea-highlight {
	background-color: rgba(24, 119, 232, 0.2);
	border-radius: 3px;
	padding-bottom: 1px;
	box-shadow: 0 0 0 0.5px rgba(24, 119, 232, 0.2);
	align-items: center;
	color: transparent;
}

.scroll-to-bottom-button {
	display: flex;
	position: absolute;
	left: 45%;
	bottom: 140px;
	z-index: 2000;
}

.reserve-seat-space {
	padding: 12px 18px 50px;
	position: relative;
}

.component-container {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: flex-start;
	margin: 1rem;
	padding: 1rem;
  }

  .component-container > * {
	margin: 0.25rem 0;
  }

  .component-item {
	margin-bottom: 0.5rem;
	width: 100%;
  }

  .component-item > p {
	margin-bottom: 0.5rem;
  }

  .error-text {
	color: red;
  }

  .warn-text {
	color: yellow;
  }

  label {
	display: inline-block;
	max-width: 100%;
	margin-bottom: 0.5rem;
	font-weight: bolder;
  }

  a {
	color: var(--vscode-textLink-foreground)
  }

  .show-detail-sel {
	border: 1px solid var(--vscode-editorGutter-modifiedBackground)
  }
/* html {
	font-size: var(--vscode-font-size);
}

.btn {
	@apply cursor-pointer transition-colors;

	&.btn-primary {
		@apply bg-vscode-button-bg text-vscode-button-fg;

		&:not(:disabled) {
			@apply hover:bg-vscode-button-hoverBg;
		}
	}

	&.btn-sm {
		@apply px-3 py-1 text-sm;
	}

	&.btn-secondary {
		@apply bg-vscode-button-secondary-bg text-vscode-button-secondary-fg;

		&:not(:disabled) {
			@apply hover:bg-vscode-button-secondary-hoverBg;
		}
	}

	&:disabled {
		@apply opacity-75 cursor-not-allowed;
	}
}

.input {
	@apply bg-vscode-input-bg text-vscode-input-fg border-vscode-input-border focus:outline-vscode-focus-border;
}

.dropdown {
	@apply bg-vscode-dropdown-bg text-vscode-dropdown-foreground border-vscode-dropdown-border focus:outline-vscode-focus-border;
} */
