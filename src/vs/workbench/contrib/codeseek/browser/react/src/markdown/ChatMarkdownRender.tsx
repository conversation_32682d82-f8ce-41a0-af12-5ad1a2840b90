/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, { JSX, useState, memo } from 'react'
import { marked, MarkedToken, Token } from 'marked'
import { BlockCode } from './BlockCode.js'
import { ChatMessageLocation, } from '../../../aiRegexService.js'
import { nameToVscodeLanguage } from '../../../../common/helpers/detectLanguage.js'
import { ApplyBlockHoverButtons } from './ApplyBlockHoverButtons.js'
import { useAccessor } from '../util/services.js'
import { convertFilePathToUri } from '../../../../common/helpers/path.js'
import DOMPurify from '../../../../../../../base/browser/dompurify/dompurify.js'

type ApplyBoxLocation = ChatMessageLocation & { tokenIdx: string }

const getApplyBoxId = ({ containerId, threadId, messageIdx, tokenIdx }: ApplyBoxLocation) => {
	return `${containerId}-${threadId}-${messageIdx}-${tokenIdx}`
}


export const CodeSpan = ({ children, className }: { children: React.ReactNode, className?: string }) => {
	return <code className={`
			bg-codeseek-codeSpanBG
			text-vscode-text-link-fg
			text-opacity-80
			px-1
			rounded-sm
			break-all
			${className}
		`}
	>
		{children}
	</code>
}

// 缓存重复的RenderToken

const RenderToken = memo(
	({ token, nested, noSpace, chatMessageLocation, tokenIdx }: { token: Token | string, nested?: boolean, noSpace?: boolean, chatMessageLocation?: ChatMessageLocation, tokenIdx: string }): JSX.Element => {

		// deal with built-in tokens first (assume marked token)
		const t = token as MarkedToken
		const accessor = useAccessor();
		const [applying, setApplying] = useState(false)

		if (t.type === "space") {
			return <span>{t.raw}</span>
		}

		if (t.type === "code") {

			const applyBoxId = chatMessageLocation ? getApplyBoxId({
				containerId: chatMessageLocation.containerId,
				threadId: chatMessageLocation.threadId,
				messageIdx: chatMessageLocation.messageIdx,
				tokenIdx: tokenIdx,
			}) : null
			const filePath = (t as any)?.filePath || '';
			const workspaceService = accessor.get('IWorkspaceContextService');
			const uri = filePath ? convertFilePathToUri(filePath.split('(')[0].trim(), workspaceService) : undefined;
			return <BlockCode
				initValue={t.text}
				language={t.lang === undefined ? undefined : nameToVscodeLanguage[t.lang]}
				uri={uri}
				applying={applying}
				tokenId={tokenIdx}
				buttonsOnHover={applyBoxId && uri && <ApplyBlockHoverButtons
					setApplying={setApplying}
					applyBoxId={applyBoxId}
					codeStr={t.text}
					uri={uri}
					showCopyOnHover={true}
				/>}
			/>
		}

		if (t.type === "heading") {
			const HeadingTag = `h${t.depth}` as keyof JSX.IntrinsicElements
			const headingClasses: { [h: string]: string } = {
				h1: "text-4xl font-semibold mt-6 mb-4 pb-2 border-b border-codeseek-bg-2",
				h2: "text-3xl font-semibold mt-6 mb-4 pb-2 border-b border-codeseek-bg-2",
				h3: "text-2xl font-semibold mt-6 mb-4",
				h4: "text-xl font-semibold mt-6 mb-4",
				h5: "text-lg font-semibold mt-6 mb-4",
				h6: "text-base font-semibold mt-6 mb-4 text-gray-600"
			}
			return <HeadingTag className={headingClasses[HeadingTag]}>{t.text}</HeadingTag>
		}

		if (t.type === "table") {
			return (
				<div className={`${noSpace ? '' : 'my-2'} overflow-x-auto`}>
					<table className="min-w-full border border-codeseek-bg-2">
						<thead>
							<tr className="bg-codeseek-bg-1">
								{t.header.map((cell: any, index: number) => (
									<th
										key={index}
										className="px-1 py-[1px] border border-codeseek-bg-2 font-semibold"
										style={{ textAlign: t.align[index] || "left" }}
									>
										{cell.text}
									</th>
								))}
							</tr>
						</thead>
						<tbody>
							{t.rows.map((row: any[], rowIndex: number) => (
								<tr key={rowIndex} className='bg-codeseek-bg-1'>
									{row.map((cell: any, cellIndex: number) => (
										<td
											key={cellIndex}
											className="px-1 py-[1px] border border-codeseek-bg-2"
											style={{ textAlign: t.align[cellIndex] || "left" }}
										>
											{cell.text}
										</td>
									))}
								</tr>
							))}
						</tbody>
					</table>
				</div>
			)
		}

		if (t.type === "hr") {
			return <hr className="my-6 border-t border-codeseek-bg-2" />
		}

		if (t.type === "blockquote") {
			return <blockquote className={`pl-4 border-l-4 border-codeseek-bg-2 italic ${noSpace ? '' : 'my-2'}`}>{t.text}</blockquote>
		}

		if (t.type === "list") {
			const ListTag = t.ordered ? "ol" : "ul"
			return (
				<ListTag
					start={t.start ? t.start : undefined}
					className={`list-inside pl-2 ${noSpace ? '' : 'my-1'} ${t.ordered ? "list-decimal" : "list-disc"}`}
				>
					{t.items.map((item, index) => (
						<li key={index} className={`${noSpace ? '' : 'my-1'}`}>
							{item.task && (
								<input type="checkbox" checked={item.checked} readOnly className="mr-2 form-checkbox" />
							)}
							<span className="ml-1">
								<ChatMarkdownRender chatMessageLocation={chatMessageLocation} string={item.text} nested={true} />
							</span>
						</li>
					))}
				</ListTag>
			)
		}

		if (t.type === "paragraph") {
			const contents = <>
				{t.tokens.map((token, index) => (
					<RenderToken key={index} token={token} tokenIdx={`${tokenIdx ? `${tokenIdx}-` : ''}${index}`} /> // assign a unique tokenId to nested components
				))}
			</>
			if (nested) return contents

			return <p className={`${noSpace ? '' : 'my-2'}`}>
				{contents}
			</p>
		}

		if (t.type === "html") {
			return <p className={`${noSpace ? '' : 'my-2'}`}>
				<div className='py-[2px]' dangerouslySetInnerHTML={{ __html: htmlFormater(t.raw) }} />
			</p>
		}

		if (t.type === "text" || t.type === "escape") {
			return <span>{t.raw}</span>
		}

		if (t.type === "def") {
			return <></> // Definitions are typically not rendered
		}

		if (t.type === "link") {
			return (
				<a
					className='underline'
					onClick={() => { window.open(t.href) }}
					href={t.href}
					title={t.title ?? undefined}
				>
					{t.text}
				</a>
			)
		}

		if (t.type === "image") {
			return <img
				src={t.href}
				alt={t.text}
				title={t.title ?? undefined}
				className={`max4w-full h-auto rounded ${noSpace ? '' : 'my-2'}`}
			/>
		}

		if (t.type === "strong") {
			return <strong className="font-semibold">{t.text}</strong>
		}

		if (t.type === "em") {
			return <em className="italic">{t.text}</em>
		}

		// inline code
		if (t.type === "codespan") {
			return (
				<CodeSpan>
					{t.text}
				</CodeSpan>
			)
		}

		if (t.type === "br") {
			return <br />
		}

		// strikethrough
		if (t.type === "del") {
			return <del className="line-through">{t.text}</del>
		}

		// default
		return (
			<div className="bg-orange-50 rounded-sm overflow-hidden p-2">
				<span className="text-sm text-orange-500">Unknown type:</span>
				{t.raw}
			</div>
		)
	},
	// 自定义比较函数，只有 token/tokenIdx 变化才重新渲染
	(prevProps, nextProps) => {
		return (
			prevProps.token === nextProps.token &&
			prevProps.tokenIdx === nextProps.tokenIdx &&
			prevProps.nested === nextProps.nested &&
			prevProps.noSpace === nextProps.noSpace &&
			prevProps.chatMessageLocation === nextProps.chatMessageLocation
		);
	}
);

export const ChatMarkdownRender = ({ string, nested = false, noSpace, chatMessageLocation }: { string: string, nested?: boolean, noSpace?: boolean, chatMessageLocation?: ChatMessageLocation }) => {
	const tokens = marked.lexer(string); // https://marked.js.org/using_pro#renderer
	tokens.forEach(token => {
		if (token.type === "code") {
			const [lang, ...filePathParts] = (token.lang || "").split(":");
			const filePathAndLine = filePathParts.join(":");
			const filePath = filePathAndLine.trim().split(" ")[0];
			token.lang = lang;
			// @ts-ignore
			token.filePath = filePath;

		}
	});
	return (
		<>
			{tokens.map((token, index) => (
				<RenderToken key={index} token={token} nested={nested} noSpace={noSpace} chatMessageLocation={chatMessageLocation} tokenIdx={index + ''} />
			))}
		</>
	)
}


function htmlFormater(value: string) {
	const sanitized = DOMPurify.sanitize(
		value
			.replace(/\n$/, '\n\n')
			.replace(/[<>&]/g, (c) => ({ '<': '<', '>': '>', '&': '&' })[c] || c),
		{
			RETURN_TRUSTED_TYPE: true
		}
	);
	return sanitized as unknown as string;
}
