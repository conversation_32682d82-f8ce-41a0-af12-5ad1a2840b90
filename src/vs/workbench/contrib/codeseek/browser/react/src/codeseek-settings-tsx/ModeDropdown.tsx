/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { useCallback } from 'react'
import { useAccessor } from '../util/services.js'
import { _CodeseekSelectBox, CodeseekCustomDropdownBox } from '../util/inputs.js'
import { ChatMode } from '../../../../common/codeseekSettingsService.js'


export const ModeDropdown = ({ className, containerId }: { className?: string, containerId?: string }) => {
	const codeseekSettingsService = useAccessor().get('ICodeseekSettingsService')
	const options = [ChatMode.Ask, ChatMode.Agent]
	const selectedOption = containerId
		? codeseekSettingsService.getChatModeForContainer(containerId)
		: codeseekSettingsService.state.chatMode

	const onChangeOption = useCallback((newOption: ChatMode) => {
		codeseekSettingsService.setChatMode(newOption, containerId ? { containerId } : undefined)
	}, [codeseekSettingsService, containerId])

	return <CodeseekCustomDropdownBox
		options={options}
		iconClasses={{
			'Ask': 'codicon codicon-comment',
			'Agent': 'codicon codicon-robot',
		}}
		selectedOption={selectedOption}
		onChangeOption={onChangeOption}
		getOptionDisplayName={option => option}
		getOptionDropdownName={option => option}	
		getOptionsEqual={(a, b) => a === b}
		className={`text-xs text-codeseek-fg-3 px-1 pb-[1px] ${className}`}
		matchInputWidth={false}
	/>
}
