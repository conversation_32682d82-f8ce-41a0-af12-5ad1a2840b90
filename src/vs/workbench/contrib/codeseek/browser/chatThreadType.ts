import { TaskInfo } from '../../../api/browser/mainThreadCodeSeekTaskService.js';
import { ChatMode } from '../common/codeseekSettingsService.js';
import { ToolCallType } from '../common/llmMessageTypes.js';
import { StagingSelectionItem } from '../common/selectedFileService.js';
import { AskResponse, ToolCallReturnType, ToolName } from '../common/toolsServiceTypes.js';

export type CodeSeekAsk =
	| 'followup'
	| 'command'
	| 'command_output'
	| 'completion_result'
	| 'tool'
	| 'api_req_failed'
	| 'resume_task'
	| 'resume_completed_task'
	| 'mistake_limit_reached'
	| 'browser_action_launch'
	| 'use_mcp_server';

export type userMessageOpts = {
	from: 'Fix' | 'Chat';
	userMessage: string;
	linterErrors?: string;
};

export type ToolMessage<T extends ToolName> = {
	role: 'tool';
	content: string | undefined; // result
	containerId: string;
	threadId: string;
	result: ToolCallReturnType[T] | null; // text message of result
} & ToolCallType;

export type AskMessage = {
	type: string;
	content: ToolCallType | null; // content displayed to the LLM on future calls - allowed to be '', will be replaced with (empty)
	// operate: string | null; // content displayed to user  - allowed to be '', will be ignored
};

// WARNING: changing this format is a big deal!!!!!! need to migrate old format to new format on users' computers so people don't get errors.
export type ChatMessage =
	| {
		role: 'system';
		content: string;
		displayContent?: undefined;
	} | {
		role: 'user';
		content: string | null; // content displayed to the LLM on future calls - allowed to be '', will be replaced with (empty)
		displayContent: string | null; // content displayed to user  - allowed to be '', will be ignored
		state: {
			stagingSelections: StagingSelectionItem[];
			isBeingEdited: boolean;
		};
	} | {
		role: 'assistant';
		content: string | null; // content received from LLM  - allowed to be '', will be replaced with (empty)
		displayContent: string | null; // content displayed to user (this is the same as content for now) - allowed to be '', will be ignored
	}
	| ToolMessage<ToolName>;

export type UserMessageType = ChatMessage & { role: 'user' };
export type UserMessageState = UserMessageType['state'];

export type ChatContainers = {
	[containerId: string]: {
		threadsState: ThreadsState;
	};
};

// a 'thread' means a chat message history
export type ChatThreads = {
	[id: string]: {
		id: string; // store the id here too
		createdAt: string; // ISO string
		lastModified: string; // ISO string
		messagesLength: number;
		firstUserMessage: string;
		state: {
			stateSelections: StateSelections;
			focusedMessageIdx: number | undefined; // index of the message that is being edited (undefined if none)
			isCheckedOfSelectionId: { [selectionId: string]: boolean };
			askMessage?: AskMessage;
			askResponse?: AskResponse;
			askResponseText?: string;
		};
	};
};


export type StateSelections = {
	list: StagingSelectionItem[];
	followEditorActive: boolean;
};

export type ThreadType = ChatThreads[string];

export type ContainerState = {
	allContainers: ChatContainers;
	currentContainerId: string;
};

export type ThreadsState = {
	allThreads: ChatThreads;
	currentThreadId: string; // intended for internal use only
	currentThreadMessages: ChatMessage[];
};

export type ThreadStreamState = {
	[containerId: string]: {
		[threadId: string]: undefined | {
			error?: { message: string; fullError: Error | null };
			messageSoFar?: string;
			streamingToken?: string;
			toolCall?: ToolCallType & { result: ToolCallReturnType[ToolName] | null; content: string };
			isStreaming?: boolean;
		};
	};
};


export type MessageType = {
	userMessageOpts: userMessageOpts;
	chatMode: ChatMode;
	chatSelections?: { prevSelns?: StagingSelectionItem[]; currSelns?: StagingSelectionItem[] };
	agentParamsFromPlugin?: Record<string, any>;
	taskInfo?: TaskInfo;
}

