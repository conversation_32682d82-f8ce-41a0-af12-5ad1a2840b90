import { Disposable } from '../../../../../base/common/lifecycle.js';
import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
import { IChatThreadService } from '../chatThreadService.js';
import { ChatMessage, ToolMessage } from '../chatThreadType.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { FeatureNames } from '../../common/codeseekSettingsTypes.js';
import { ILLMMessageService } from '../../common/llmMessageService.js';
import { summaryUserMessage, summarySystemMessage, enhancementSystemMessage, enhancementUserMessage } from '../../common/prompt/prompts.js';
import { IDirectoryStrService } from '../../common/directoryStrService.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { IEditorService, EditorsOrder } from '../../../../../workbench/services/editor/common/editorService.js';


export interface IChatAgentService {
	readonly _serviceBrand: undefined;
	chatHistorySummary(containerId: string, userInput: string): Promise<string>;
	promptEnhancement(containerId: string, userInput: string): Promise<string>;
	openTabs(): IOpenTabInfo[];
}

export interface IOpenTabInfo {
	name: string;
	resource: string;
	isDirty: boolean;
	groupId?: number;
}

export const IChatAgentService = createDecorator<IChatAgentService>('chatAgentService');


export class ChatAgentService extends Disposable implements IChatAgentService {
	readonly _serviceBrand: undefined;
	constructor(
		@IChatThreadService private readonly chatThreadService: IChatThreadService,
		@ILLMMessageService private readonly llmMessageService: ILLMMessageService,
		@ICodeseekLogger private readonly codeseekLogService: ICodeseekLogger,
		@IDirectoryStrService private readonly directoryStrService: IDirectoryStrService,
		@IWorkspaceContextService private readonly workspaceService: IWorkspaceContextService,
		@IEditorService private readonly editorService: IEditorService,
	) {
		super();
	}

	async chatHistorySummary(containerId: string, userInput: string): Promise<string> {
		const previousMessages = this.formatChatHistory(containerId);
		const userMessage = summaryUserMessage(previousMessages, userInput);
		let res_: () => void;
		const awaitable = new Promise<void>((res, rej) => { res_ = res; });
		let summary = '';
		const result = this.llmMessageService.sendLLMMessage({
			containerId,
			useProviderFor: FeatureNames.CtrlL,
			messagesType: 'agentMessages',
			messages: [{ role: 'system', content: summarySystemMessage }, { role: 'user', content: userMessage }],
			onText: () => { },
			onToolCall: () => { },
			onFinalMessage: (params) => {
				this.codeseekLogService.info('chatHistorySummary, got result: ', params.fullText);
				summary = params.fullText;
				res_();
			},
			onError: (params) => {
				this.codeseekLogService.error('chatHistorySummary, error: ', params.message, params.fullError);
				res_();
			},
			logging: { loggingName: 'ChatAgentService' }
		});
		this.codeseekLogService.info('chatHistorySummary, sendLLMMessage result: ', result);
		await awaitable;
		return summary;
	}

	/**
	 * 将 ChatMessage 数组格式化为可读的历史记录字符串
	 * @param containerId 容器ID
	 * @returns 格式化后的历史记录字符串
	 */
	formatChatHistory(containerId: string): string {
		const messages = this.chatThreadService.getCurrentThreadMessages(containerId);
		if (!messages || messages.length === 0) {
			return '';
		}

		return messages.filter(msg => msg.role !== 'system').map((msg, index) => {
			// 基本格式：[角色]: 内容
			const roleLabel = this.getRoleLabel(msg.role);
			let content = '';

			if (msg.role === 'user') {
				// 用户消息优先使用 displayContent
				content = msg.displayContent ?? msg.content ?? '';
			} else if (msg.role === 'assistant') {
				// 助手消息优先使用 displayContent
				content = msg.displayContent ?? msg.content ?? '';
			} else if (msg.role === 'tool') {
				// 工具消息需要特殊处理
				content = this.formatToolMessage(msg);
			}

			// 添加消息序号
			const messageNumber = `#${index + 1}`;

			// 基本格式
			let formattedMessage = `${messageNumber} ${roleLabel}: ${content}`;

			// 用户消息，添加选择内容信息
			if (msg.role === 'user') {
				if (msg.state?.stagingSelections?.length > 0) {
					const selectionsInfo = `\n    选择内容: ${msg.state.stagingSelections.length} 项`;
					formattedMessage += selectionsInfo;
				}
			}

			return formattedMessage;
		}).join('\n\n');
	}

	/**
	 * 获取角色的显示标签
	 */
	private getRoleLabel(role: string): string {
		switch (role) {
			case 'user': return '用户';
			case 'assistant': return '助手';
			case 'system': return '系统';
			case 'tool': return '工具';
			default: return role;
		}
	}

	/**
	 * 格式化工具消息
	 */
	private formatToolMessage(toolMsg: ChatMessage): string {
		if (toolMsg.role === 'tool') {
			const name = (toolMsg as ToolMessage<any>).name || '工具';
			const result = toolMsg.content ?? '无结果';
			// 始终使用详细格式
			return `${name}\n    结果: ${result}`;
		}
		return toolMsg.content ?? '';
	}

	// 获取打开的标签页
	openTabs(): IOpenTabInfo[] {
		try {
			// 获取所有打开的编辑器，按顺序排列
			const editorIdentifiers = this.editorService.getEditors(EditorsOrder.SEQUENTIAL);

			return editorIdentifiers.map(({ editor, groupId }) => ({
				name: editor.getName(),
				resource: editor.resource?.toString() || '',
				isDirty: editor.isDirty(),
				groupId: groupId
			}));
		} catch (error) {
			this.codeseekLogService.error('获取打开的标签页失败:', error);
			return [];
		}
	}

	async promptEnhancement(containerId: string, userInput: string): Promise<string> {
		const chatHistory = await this.chatHistorySummary(containerId, userInput);
		const cutOffMessage = `...Directories string cut off, use tools to read more...`;
		const directoryStrDetail = await this.directoryStrService.getAllDirectoriesStr({ cutOffMessage });
		// 获取工作区根目录
		const workingDirectory = this.workspaceService.getWorkspace().folders[0]?.name || '';
		const userMessage = enhancementUserMessage(userInput, chatHistory, '', workingDirectory, directoryStrDetail);
		let res_: () => void;
		const awaitable = new Promise<void>((res, rej) => { res_ = res; });
		let prompt = '';
		const result = this.llmMessageService.sendLLMMessage({
			containerId,
			useProviderFor: FeatureNames.CtrlL,
			messagesType: 'agentMessages',
			messages: [{ role: 'system', content: enhancementSystemMessage }, { role: 'user', content: userMessage }],
			onText: () => { },
			onToolCall: () => { },
			onFinalMessage: (params) => {
				this.codeseekLogService.info('promptEnhancement, got result: ', params.fullText);
				prompt = params.fullText;
				res_();
			},
			onError: (params) => {
				this.codeseekLogService.error('chatHistopromptEnhancementySummary, error: ', params.message, params.fullError);
				res_();
			},
			logging: { loggingName: 'ChatAgentService' }
		});
		this.codeseekLogService.info('promptEnhancement, sendLLMMessage result: ', result);
		await awaitable;
		return prompt;
	}



}
