import { IChatAgentService, IOpenTabInfo } from './chatAgentService.js';

/**
 * 使用 openTabs 功能的示例
 */
export class OpenTabsExample {
	
	constructor(
		private readonly chatAgentService: IChatAgentService
	) {}

	/**
	 * 获取并显示所有打开的标签页信息
	 */
	displayOpenTabs(): void {
		try {
			const openTabs = this.chatAgentService.openTabs();
			
			console.log('=== 打开的标签页信息 ===');
			console.log(`总共打开了 ${openTabs.length} 个标签页`);
			
			if (openTabs.length === 0) {
				console.log('当前没有打开的标签页');
				return;
			}
			
			openTabs.forEach((tab, index) => {
				console.log(`\n${index + 1}. ${tab.name}`);
				console.log(`   URI: ${tab.resource}`);
				console.log(`   状态: ${tab.isDirty ? '已修改' : '未修改'}`);
				console.log(`   组ID: ${tab.groupId}`);
			});
			
		} catch (error) {
			console.error('获取打开的标签页失败:', error);
		}
	}

	/**
	 * 获取已修改的文件列表
	 */
	getDirtyFiles(): IOpenTabInfo[] {
		const openTabs = this.chatAgentService.openTabs();
		return openTabs.filter(tab => tab.isDirty);
	}

	/**
	 * 获取特定类型的文件
	 */
	getFilesByExtension(extension: string): IOpenTabInfo[] {
		const openTabs = this.chatAgentService.openTabs();
		return openTabs.filter(tab => {
			const uri = tab.resource;
			return uri.toLowerCase().endsWith(`.${extension.toLowerCase()}`);
		});
	}

	/**
	 * 按编辑器组分组显示标签页
	 */
	displayTabsByGroup(): void {
		const openTabs = this.chatAgentService.openTabs();
		
		// 按组ID分组
		const tabsByGroup = new Map<number, IOpenTabInfo[]>();
		
		openTabs.forEach(tab => {
			const groupId = tab.groupId || 0;
			if (!tabsByGroup.has(groupId)) {
				tabsByGroup.set(groupId, []);
			}
			tabsByGroup.get(groupId)!.push(tab);
		});
		
		console.log('=== 按编辑器组显示标签页 ===');
		tabsByGroup.forEach((tabs, groupId) => {
			console.log(`\n编辑器组 ${groupId} (${tabs.length} 个标签页):`);
			tabs.forEach((tab, index) => {
				const status = tab.isDirty ? ' [已修改]' : '';
				console.log(`  ${index + 1}. ${tab.name}${status}`);
			});
		});
	}

	/**
	 * 获取工作区统计信息
	 */
	getWorkspaceStats(): {
		totalTabs: number;
		dirtyTabs: number;
		fileTypes: Map<string, number>;
		groups: number;
	} {
		const openTabs = this.chatAgentService.openTabs();
		
		const stats = {
			totalTabs: openTabs.length,
			dirtyTabs: openTabs.filter(tab => tab.isDirty).length,
			fileTypes: new Map<string, number>(),
			groups: new Set(openTabs.map(tab => tab.groupId || 0)).size
		};
		
		// 统计文件类型
		openTabs.forEach(tab => {
			const uri = tab.resource;
			const match = uri.match(/\.([^.]+)$/);
			const extension = match ? match[1].toLowerCase() : 'unknown';
			
			stats.fileTypes.set(extension, (stats.fileTypes.get(extension) || 0) + 1);
		});
		
		return stats;
	}

	/**
	 * 显示工作区统计信息
	 */
	displayWorkspaceStats(): void {
		const stats = this.getWorkspaceStats();
		
		console.log('=== 工作区统计信息 ===');
		console.log(`总标签页数: ${stats.totalTabs}`);
		console.log(`已修改文件数: ${stats.dirtyTabs}`);
		console.log(`编辑器组数: ${stats.groups}`);
		
		console.log('\n文件类型分布:');
		Array.from(stats.fileTypes.entries())
			.sort((a, b) => b[1] - a[1]) // 按数量降序排列
			.forEach(([extension, count]) => {
				console.log(`  .${extension}: ${count} 个文件`);
			});
	}

	/**
	 * 查找包含特定文本的文件名
	 */
	findTabsByName(searchText: string): IOpenTabInfo[] {
		const openTabs = this.chatAgentService.openTabs();
		return openTabs.filter(tab => 
			tab.name.toLowerCase().includes(searchText.toLowerCase())
		);
	}

	/**
	 * 获取最近打开的文件（基于URI路径）
	 */
	getRecentFiles(limit: number = 5): IOpenTabInfo[] {
		const openTabs = this.chatAgentService.openTabs();
		// 这里简单返回前几个，实际应用中可能需要基于时间戳排序
		return openTabs.slice(0, limit);
	}

	/**
	 * 生成打开标签页的摘要报告
	 */
	generateTabsReport(): string {
		const openTabs = this.chatAgentService.openTabs();
		const stats = this.getWorkspaceStats();
		
		let report = `# 打开标签页报告\n\n`;
		report += `## 概览\n`;
		report += `- 总标签页数: ${stats.totalTabs}\n`;
		report += `- 已修改文件: ${stats.dirtyTabs}\n`;
		report += `- 编辑器组数: ${stats.groups}\n\n`;
		
		report += `## 文件类型分布\n`;
		Array.from(stats.fileTypes.entries())
			.sort((a, b) => b[1] - a[1])
			.forEach(([extension, count]) => {
				report += `- .${extension}: ${count} 个文件\n`;
			});
		
		if (stats.dirtyTabs > 0) {
			report += `\n## 已修改的文件\n`;
			const dirtyFiles = this.getDirtyFiles();
			dirtyFiles.forEach(file => {
				report += `- ${file.name}\n`;
			});
		}
		
		return report;
	}
}

/**
 * 使用示例
 */
export function exampleUsage(chatAgentService: IChatAgentService): void {
	const example = new OpenTabsExample(chatAgentService);
	
	// 显示所有打开的标签页
	example.displayOpenTabs();
	
	// 显示按组分类的标签页
	example.displayTabsByGroup();
	
	// 显示工作区统计
	example.displayWorkspaceStats();
	
	// 查找特定文件
	const tsFiles = example.getFilesByExtension('ts');
	console.log(`找到 ${tsFiles.length} 个 TypeScript 文件`);
	
	// 查找已修改的文件
	const dirtyFiles = example.getDirtyFiles();
	console.log(`有 ${dirtyFiles.length} 个文件已修改`);
	
	// 生成报告
	const report = example.generateTabsReport();
	console.log('\n' + report);
}
