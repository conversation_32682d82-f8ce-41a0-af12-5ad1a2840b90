/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { ProxyChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { METRICS_EVENT, METRICS_EVENT_TYPE, IMetricsService } from '../common/metricsService.js';
import { register<PERSON><PERSON><PERSON>, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { localize2 } from '../../../../nls.js';
import { ServicesAccessor } from '../../../../editor/browser/editorExtensions.js';
import { registerAction2, Action2 } from '../../../../platform/actions/common/actions.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { isLinux, isMacintosh, isWindows } from '../../../../base/common/platform.js';
import { platform, arch } from '../../../../base/common/process.js';
import { ICodeseekUacLoginService } from '../common/uac/UacloginTypes.js';
import { IExtensionsWorkbenchService } from '../../extensions/common/extensions.js';
import { UserInfo } from '../common/uac/UacloginTypes.js';


export class CodeseekMetricsService extends Disposable implements IMetricsService {

	readonly _serviceBrand: undefined;
	private readonly metricsService: IMetricsService;
	private userInfo: UserInfo | undefined = undefined;

	constructor(
		@IProductService private readonly productService: IProductService,
		@ICodeseekUacLoginService private readonly codeseekUacLoginService: ICodeseekUacLoginService,
		@IExtensionsWorkbenchService private readonly extensionsWorkbenchService: IExtensionsWorkbenchService,
		@IMainProcessService mainProcessService: IMainProcessService,
	) {
		super();
		this.metricsService = ProxyChannel.toService<IMetricsService>(mainProcessService.getChannel('codeseek-channel-metrics'));
		this.getUserInfo();
	}

	capture(event: METRICS_EVENT_TYPE, params: Record<string, any>, userId?: string): void {
		try {
			if (event === METRICS_EVENT.UPGRADE) {
				this.upgradeCapture(params);
			} else {
				this.metricsService.capture(event, params, this.userInfo?.userId);
			}
		} catch (error) {
			console.error('[MetricsService] Failed to capture metrics:', error);
		}
	}

	/**
	 * 获取扩展版本信息
	 */
	private async extensionInfo(): Promise<Record<string, any>> {
		try {
			// 等待插件服务初始化完成
			await this.extensionsWorkbenchService.whenInitialized;

			// 获取所有已安装的插件
			const installedExtensions = this.extensionsWorkbenchService.installed;

			const extensionsInfo = installedExtensions.map(ext => ({
				id: ext.identifier.id,
				name: ext.displayName || ext.identifier.id,
				version: ext.version,
				isBuiltin: ext.isBuiltin,
				type: ext.type,
				enabled: ext.enablementState,
				publisher: ext.publisher,
				// 只包含基本信息，避免过多数据
			}));

			// 同时获取内置插件信息作为备份
			const builtinExtensions = this.productService.builtInExtensions || [];
			const builtinInfo = builtinExtensions.map(ext => ({
				name: ext.name,
				version: ext.version,
				isBuiltin: true,
			}));

			return {
				extensions: extensionsInfo,
				builtinExtensions: builtinInfo,
				totalCount: extensionsInfo.length,
				builtinCount: extensionsInfo.filter(ext => ext.isBuiltin).length,
				userCount: extensionsInfo.filter(ext => !ext.isBuiltin).length,
			};
		} catch (error) {
			console.error('[MetricsService] Failed to collect extension info:', error);
			// 降级到只获取内置插件
			try {
				const builtinExtensions = this.productService.builtInExtensions || [];
				const builtinInfo = builtinExtensions.map(ext => ({
					name: ext.name,
					version: ext.version,
					isBuiltin: true,
				}));
				return {
					extensions: builtinInfo,
					error: 'Failed to get full extension list, fallback to builtin only',
				};
			} catch (fallbackError) {
				return {
					extensions: [],
					error: 'Failed to get any extension info',
				};
			}
		}
	}

	async getUserInfo() {
		try {
			this.userInfo = await this.codeseekUacLoginService.getUserInfo();
			console.log('[MetricsService] User info retrieved successfully:', this.userInfo);
		} catch (error) {
			console.error('[MetricsService] Failed to retrieve user info:', error);
		}
	}

	private osInfo(): Record<string, any> {
		try {
			const os = isWindows ? 'windows' : isMacintosh ? 'mac' : isLinux ? 'linux' : '';
			return { osInfo: { platform: platform, arch: arch, os } };
		}
		catch (e) {
			return { osInfo: { platform: '??', arch: '??', os: '??' } };
		}
	}

	private async upgradeCapture(params: Record<string, any>): Promise<void> {
		if (this.userInfo === undefined) {
			await this.getUserInfo();
		}
		const flowIdeVersion = this.productService.productVersion;
		const os = this.osInfo();
		const extensions = await this.extensionInfo();
		this.metricsService.capture(METRICS_EVENT.UPGRADE, { ...params, flowIdeVersion, ...os, ...extensions }, this.userInfo?.userId);
	}

	async getDebuggingProperties(): Promise<object> {
		const flowIdeVersion = this.productService.productVersion;
		const os = this.osInfo();
		const extensions = await this.extensionInfo();
		return {
			version: flowIdeVersion,
			...os,
			...extensions
		};
	}
}

registerSingleton(IMetricsService, CodeseekMetricsService, InstantiationType.Eager);

// debugging action
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: 'codeseekDebugInfo',
			f1: true,
			title: localize2('codeseekMetricsDebug', 'Codeseek: Log Debug Info'),
		});
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const metricsService = accessor.get(IMetricsService)
		const notifService = accessor.get(INotificationService)

		const debugProperties = await metricsService.getDebuggingProperties()
		console.log('Metrics:', debugProperties)
		notifService.info(`Codeseek Debug info:\n${JSON.stringify(debugProperties, null, 2)}`)
	}
});
