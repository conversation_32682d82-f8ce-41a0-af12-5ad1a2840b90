/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Emitter, Event } from '../../../../base/common/event.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { CODESEEK_OPEN_CONTAINER_ACTION_ID } from './actionIDs.js';
import { IWorkbenchLayoutService, Parts } from '../../../services/layout/browser/layoutService.js';
import { IViewsService } from '../../../services/views/common/viewsService.js';


// service that manages sidebar's state
export type CodeseekSidebarState = {
	isHistoryOpen: boolean;
	isChatOpen: boolean;
	currentTab: 'chat';
};

export interface ISidebarStateService {
	readonly _serviceBrand: undefined;

	readonly state: CodeseekSidebarState; // readonly to the user
	setState(newState: Partial<CodeseekSidebarState>): void;
	onDidChangeState: Event<void>;

	isSidebarChatOpen(): boolean;
	onDidFocusChat: Event<string>;
	onDidBlurChat: Event<string>;
	onDidChangeChatViewVisibility: Event<void>;
	onDidOpenNewChat: Event<string>;
	fireFocusContainer(containerId: string): void;
	fireFocusChat(containerId: string): void;
	fireBlurChat(containerId: string): void;
	fireOpenNewChat(containerId: string): void;

	fireChangeChatViewVisibility(): void;
}

export const ISidebarStateService = createDecorator<ISidebarStateService>('codeseekSidebarStateService');

class CodeseekSidebarStateService extends Disposable implements ISidebarStateService {
	_serviceBrand: undefined;

	static readonly ID = 'codeseekSidebarStateService';

	private readonly _onDidChangeState = new Emitter<void>();
	readonly onDidChangeState: Event<void> = this._onDidChangeState.event;

	private readonly _onFocusChat = new Emitter<string>();
	readonly onDidFocusChat: Event<string> = this._onFocusChat.event;

	private readonly _onBlurChat = new Emitter<string>();
	readonly onDidBlurChat: Event<string> = this._onBlurChat.event;

	private readonly _onDidChangeChatViewVisibility = new Emitter<void>();
	readonly onDidChangeChatViewVisibility: Event<void> = this._onDidChangeChatViewVisibility.event;

	private readonly _onDidOpenNewChat = new Emitter<string>();
	readonly onDidOpenNewChat: Event<string> = this._onDidOpenNewChat.event;


	// state
	state: CodeseekSidebarState;

	constructor(
		@ICommandService private readonly commandService: ICommandService,
		@IWorkbenchLayoutService private readonly layoutService: IWorkbenchLayoutService,
		@IViewsService private readonly viewsService: IViewsService,
	) {
		super();

		// initial state
		this.state = { isHistoryOpen: false, isChatOpen: true, currentTab: 'chat', };

		this._register(this.layoutService.onDidChangePartVisibility(() => {
			if (!this.layoutService.isVisible(Parts.AUXILIARYBAR_PART)) {
				this.setState({ isChatOpen: false });
				this.fireChangeChatViewVisibility();
			}
		}));
	}

	setState(newState: Partial<CodeseekSidebarState>) {
		// make sure view is open if the tab changes
		if ('currentTab' in newState) {
			this.commandService.executeCommand(CODESEEK_OPEN_CONTAINER_ACTION_ID);
		}

		this.state = { ...this.state, ...newState };
		this._onDidChangeState.fire();
	}

	public isSidebarChatOpen() {
		return this.state.isChatOpen;
	}

	fireFocusContainer(containerId: string): void {
		this.viewsService.openViewContainer(containerId, true);
		this.viewsService.openView(containerId, true);
	}

	fireFocusChat(containerId: string) {
		this._onFocusChat.fire(containerId);
	}

	fireBlurChat(containerId: string) {
		this._onBlurChat.fire(containerId);
	}

	fireChangeChatViewVisibility() {
		this._onDidChangeChatViewVisibility.fire();
	}

	fireOpenNewChat(containerId: string): void {
		this._onDidOpenNewChat.fire(containerId);
	}
}

registerSingleton(ISidebarStateService, CodeseekSidebarStateService, InstantiationType.Eager);
