import * as dom from '../../../../base/browser/dom.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { ChatMode } from '../common/codeseekSettingsService.js';
import { IChatThreadService } from './chatThreadService.js';
import { getFixMessageInChat } from './../common/prompt/prompts.js';
import { CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID, CODESEEK_OPEN_SIDEBAR_ACTION_ID } from './sidebarActions.js';
import { ISidebarStateService } from './sidebarStateService.js';
import { IMarker } from '../../../../platform/markers/common/markers.js';
import { ICodeseekCodeSelectionService } from './codeseekCodeSelectionService.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { StagingSelectionItem } from '../common/selectedFileService.js';
import { ICodeEditor } from '../../../../editor/browser/editorBrowser.js';
import { IEditCodeService } from './editCodeService.js';
const $ = dom.$;

export type AddExplainOpts = {
	startLine: number;
	endLine: number;
	editor: ICodeEditor;
};

export interface ICodeseekCommandService {
	readonly _serviceBrand: undefined;

	initializeFixStream: (marker: IMarker, markerList: IMarker[]) => Promise<void>;

	mountFixInChatWidget: (rootNode: HTMLElement, onFixInChatClick: () => void) => void;

	initializeExplainStream: ({ startLine, endLine, editor }: AddExplainOpts) => Promise<void>;
}

export const ICodeseekCommandService = createDecorator<ICodeseekCommandService>('codeseekCommandService');
export class CodeseekCommandService extends Disposable implements ICodeseekCommandService {
	_serviceBrand: undefined;

	constructor(
		@IChatThreadService private readonly _chatThreadService: IChatThreadService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@ICommandService private readonly _commandService: ICommandService,
		@ICodeseekCodeSelectionService private readonly _codeseekCodeSelectionService: ICodeseekCodeSelectionService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@IEditCodeService private readonly _editCodeService: IEditCodeService,
	) {
		super();
	}

	private getFixMessageInChat = (error: string) => {
		return getFixMessageInChat(error);
	};

	public async initializeFixStream(marker: IMarker, markerList: IMarker[]) {
		this._codeseekLogService.info('fix bug, the marker is', marker);
		const containerId = this._chatThreadService.getCurrentContainerId();
		if (this._chatThreadService.isCurrentThreadWorking(containerId)) {
			this._codeseekLogService.info('chat is working, please wait');
			return;
		}

		const fixMessage = this.getFixMessageInChat(marker.message);

		if (!this._sidebarStateService.isSidebarChatOpen()) {
			await this._commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
		}
		const codeSelection = this._codeseekCodeSelectionService.getCodeSelectionFromMarker(marker);
		if (!codeSelection) {
			this._codeseekLogService.info('no code selection');
			return;
		}

		const filteredMarkers = markerList.filter(m => m.endLineNumber >= marker.startLineNumber);

		const codeSelections: StagingSelectionItem[] = [];
		for (const m of filteredMarkers) {
			const selection = this._codeseekCodeSelectionService.getCodeSelectionFromMarker(m, 2, 3);
			if (selection) {
				codeSelections.push(selection);
			}
		}

		const mergedSelections = this._codeseekCodeSelectionService.mergeCodeSelections(codeSelections);

		const formattedCodeList: string[] = [];
		if (mergedSelections.length > 0) {
			for (const mergedSelection of mergedSelections) {
				if (mergedSelection.type !== 'Selection' || !mergedSelection.range) {
					continue;
				}

				const rangeMarkers = filteredMarkers.filter(m => {
					return !(m.endLineNumber < mergedSelection.range!.startLineNumber ||
						m.startLineNumber > mergedSelection.range!.endLineNumber);
				});

				if (rangeMarkers.length > 0) {
					const formatted = this._codeseekCodeSelectionService.formatCodeWithMarkers(mergedSelection, rangeMarkers);
					if (formatted) {
						formattedCodeList.push(formatted);
					}
				}
			}
		}

		const linterErrors = formattedCodeList.join('\n\n');
		await this._commandService.executeCommand(CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID);
		this._chatThreadService.addSelectionToChat(containerId, codeSelection);
		await this._chatThreadService.addUserMessageAndStreamResponse({ containerId, userMessageOpts: { from: 'Fix', userMessage: fixMessage, linterErrors }, chatMode: ChatMode.Ask });
	}

	public mountFixInChatWidget = (rootNode: HTMLElement, onFixInChatClick: () => void) => {
		const fixInChatButton = dom.append(rootNode, $('span'));
		const commonButtonStyle = {
			display: 'flex',
			height: '20px',
			width: '100px',
			color: 'var(--vscode-button-foreground)',
			backgroundColor: 'rgba(0, 122, 204, 1)',
			border: '1px solid var(--vscode-settings-sashBorder)',
			borderRadius: '3px',
			fontSize: '12px',
			cursor: 'pointer',
			alignItems: 'center',
			justifyContent: 'center',
			userSelect: 'none',
			marginTop: '5px',
			marginBottom: '5px'
		};
		Object.assign(fixInChatButton.style, commonButtonStyle);
		fixInChatButton.innerText = 'Fix in Chat';
		fixInChatButton.addEventListener('click', onFixInChatClick);

		const addHoverEffect = (button: HTMLElement) => {
			button.addEventListener('mouseover', () => {
				button.style.border = '1px solid var(--vscode-commandCenter-activeBorder)';
				button.style.backgroundColor = 'rgba(0, 122, 204, 0.8)';
			});
			button.addEventListener('mouseout', () => {
				button.style.border = '1px solid var(--vscode-settings-sashBorder)';
				button.style.backgroundColor = 'rgba(0, 122, 204, 1)';
			});
		};

		addHoverEffect(fixInChatButton);
		rootNode.removeAttribute('style');
	};

	public async initializeExplainStream({ startLine, endLine, editor }: AddExplainOpts) {
		editor.revealLine(startLine);
		editor.setSelection({ startLineNumber: startLine, endLineNumber: startLine, startColumn: 1, endColumn: 1 });
		const uri = editor.getModel()?.uri;
		if (!uri) {
			this._codeseekLogService.error('no uri');
			return;
		}
		const instructions = 'Explain the code in the selection.';
		this._editCodeService.startApplying({
			from: 'QuickEdit',
			type: 'explain',
			instructions,
			uri,
			startLine,
			endLine,
		})
	}
}

registerSingleton(ICodeseekCommandService, CodeseekCommandService, InstantiationType.Eager);
