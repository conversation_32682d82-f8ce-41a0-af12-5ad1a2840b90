/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { VSBuffer } from '../../../../base/common/buffer.js';
import { isWindows } from '../../../../base/common/platform.js';
import { URI } from '../../../../base/common/uri.js';
import { EndOfLinePreference, ITextModel } from '../../../../editor/common/model.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { ISCMService } from '../../scm/common/scm.js';
import { ICodeseekLogger } from './codeseekLogService.js';
import { filenameToVscodeLanguage } from './helpers/detectLanguage.js';
import { getWorkspaceUri } from './helpers/path.js';

export const defaultExcludeFolders = [
	'.git', '.idea', '.vscode', '.build', 'node_modules',
	'dist', 'build', 'target', 'out', 'logs', 'tmp', 'cache',
	'temp', 'tempfiles', '.vscode-server', '.vscode-server-insiders',
	'.husky'
];

// linebreak symbols
export const allLinebreakSymbols = ['\r\n', '\n'];
export const _ln = isWindows ? allLinebreakSymbols[0] : allLinebreakSymbols[1];

export interface IProjectStructure {
	root: IDirectoryNode | null;
}

export interface IDirectoryNode {
	name: string;
	uri: URI;
	type: 'directory';
	children: Array<IDirectoryNode | IFileNode>;
}

export interface IFileNode {
	name: string;
	uri: URI;
	type: 'file';
}

export enum FileType {
	Unknown = 0,
	File = 1,
	Directory = 2,
	SymbolicLink = 64
}

export interface ICodeseekFileService {
	readonly _serviceBrand: undefined;

	readFile(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): Promise<string>;
	readModel(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): string | null;
	getModel(uri: URI): ITextModel | null;
	updateFile(uri: URI, content: string, start: number, end: number): Promise<string>;
	getGitModifiedFiles(): Promise<URI[]>;
	getProjectStructure(maxDepth: number, excludeFolders?: string[]): Promise<IProjectStructure>;
	readDirectory(uri: URI): Promise<[string, FileType][]>;
}

export const ICodeseekFileService = createDecorator<ICodeseekFileService>('CodeseekFileService');

// implemented by calling channel
export class CodeseekFileService implements ICodeseekFileService {
	readonly _serviceBrand: undefined;

	constructor(
		@IModelService private readonly modelService: IModelService,
		@IFileService private readonly fileService: IFileService,
		@ISCMService private readonly scmService: ISCMService,
		@ICodeseekLogger private readonly logService: ICodeseekLogger,
		@IWorkspaceContextService private readonly workspaceService: IWorkspaceContextService,
	) {

	}

	async readFile(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): Promise<string> {
		const modelResult = this.readModel(uri, range);
		if (modelResult) return modelResult;

		// if no model, read the raw file
		const fileResult = await this._readFileRaw(uri, range);
		if (fileResult) return fileResult;

		return '';
	};

	async _readFileRaw(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): Promise<string | null> {
		try {
			const res = await this.fileService.readFile(uri);
			if (range) {
				return res.value.toString()
					.split(_ln)
					.slice(range.startLineNumber - 1, range.endLineNumber)
					.join(_ln);
			}
			return res.value.toString();
		} catch (e) {
			return null;
		}
	};

	getModel(uri: URI): ITextModel | null {
		return this.modelService.getModel(uri);
	}

	readModel(uri: URI, range?: { startLineNumber: number; endLineNumber: number }): string | null {

		// read saved model (sometimes null if the user reloads application)
		let model = this.modelService.getModel(uri);

		// check all opened models for the same `fsPath`
		if (!model) {
			const models = this.modelService.getModels();
			for (const m of models) {
				if (m.uri.fsPath === uri.fsPath) {
					model = m;
					break;
				}
			}
		}

		// if still not found, return
		if (!model) { return null; }

		// if range, read it
		if (range) {
			return model.getValueInRange({
				startLineNumber: range.startLineNumber,
				endLineNumber: range.endLineNumber,
				startColumn: 1,
				endColumn: Number.MAX_VALUE
			}, EndOfLinePreference.LF);
		} else {
			return model.getValue(EndOfLinePreference.LF);
		}

	};

	async updateFile(uri: URI, content: string, start: number, end: number): Promise<string> {

		try { // this throws an error if no file exists (eg it was deleted)

			const res = await this.fileService.readFile(uri);
			const fileContent = res.value.toString().split(_ln);

			fileContent.splice(start - 1, end - start + 1, ...content.split(_ln));

			const newFileContent = fileContent.join(_ln);
			this.fileService.writeFile(uri, VSBuffer.fromString(newFileContent)).then(res => {
				return 'success';
			}).catch(e => {
				return 'failed';
			});
			return 'success';
		} catch (e) {
			return 'failed';
		}
	};

	async getGitModifiedFiles(): Promise<URI[]> {
		this.logService.info('Getting Git modified files');

		const modifiedFiles: URI[] = [];

		// 获取所有SCM资源组
		const repositories = this.scmService.repositories;

		for (const repository of repositories) {
			const resourceGroups = repository.provider.groups;

			for (const group of resourceGroups) {
				for (const resource of group.resources) {
					if (resource.sourceUri) {
						modifiedFiles.push(resource.sourceUri);
					}
				}
			}
		}

		return modifiedFiles;
	}

	async getProjectStructure(maxDepth: number = Infinity, excludeFolders: string[] = defaultExcludeFolders): Promise<IProjectStructure> {
		const { workspaceName, workspaceUri } = getWorkspaceUri(this.workspaceService);
		if (!workspaceUri) {
			return { root: null };
		}

		const root: IDirectoryNode = {
			name: workspaceName,
			uri: workspaceUri,
			type: 'directory',
			children: []
		};

		await this.buildDirectoryTree(root, maxDepth, excludeFolders);

		return { root };
	}

	private async buildDirectoryTree(node: IDirectoryNode, depth: number, excludeFolders: string[] = []): Promise<void> {
		if (depth <= 0) {
			return; // 超过指定深度，停止递归
		}

		const entries = await this.readDirectory(node.uri);

		for (const [name, type] of entries) {
			// 如果文件夹名称在排除列表中，则跳过
			if (type === FileType.Directory && excludeFolders.includes(name)) {
				continue;
			}

			const childUri = URI.joinPath(node.uri, name);

			if (type === FileType.Directory) {
				const dirNode: IDirectoryNode = {
					name,
					uri: childUri,
					type: 'directory',
					children: []
				};
				node.children.push(dirNode);

				// 递归构建子目录
				await this.buildDirectoryTree(dirNode, depth - 1, excludeFolders);
			} else if (type === FileType.File) {
				const language = filenameToVscodeLanguage(childUri.fsPath);
				if (language) {
					const fileNode: IFileNode = {
						name,
						uri: childUri,
						type: 'file',
					};
					node.children.push(fileNode);
				}
			}
		}
	}


	async readDirectory(uri: URI): Promise<[string, FileType][]> {
		const result = await this.fileService.resolve(uri, { resolveMetadata: true });
		if (!result.isDirectory) {
			throw new Error(`Not a directory: ${uri.toString()}`);
		}

		return result.children?.map(entry => {
			const type = entry.isDirectory ? FileType.Directory : FileType.File;
			return [entry.name, type];
		}) || [];
	}
}

registerSingleton(ICodeseekFileService, CodeseekFileService, InstantiationType.Eager);
