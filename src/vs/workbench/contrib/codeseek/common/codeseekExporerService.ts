import DOMPurify from '../../../../base/browser/dompurify/dompurify.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { DisposableStore } from '../../../../base/common/lifecycle.js';
import { dirname } from '../../../../base/common/resources.js';
import { URI } from '../../../../base/common/uri.js';
import { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';
import { FileChangeType, IFileService } from '../../../../platform/files/common/files.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { IThemeService } from '../../../../platform/theme/common/themeService.js';
import { IUriIdentityService } from '../../../../platform/uriIdentity/common/uriIdentity.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { IFilesConfigurationService } from '../../../services/filesConfiguration/common/filesConfigurationService.js';
import { IWorkbenchThemeService } from '../../../services/themes/common/workbenchThemeService.js';

import { ExplorerItem, ExplorerModel } from '../../files/common/explorerModel.js';
import { SortOrder } from '../../files/common/files.js';
import { createHighlighter, bundledLanguages } from '../browser/react/out/shiki/index.js';
import type { Root, Element } from 'hast';

export type FilePathItem = {
	isDirectory: boolean;
	relativePath: string;
	uri: URI;
	/**
	 * 当前是否为活动编辑器
	 */
	isActive: boolean;
	/**
	 * 当前是否为打开状态
	 */
	isOpened: boolean;
	/**
	 * 是否打开过
	 */
	hasOpened: boolean;
};

export interface ICodeSeekExporerService {
	_serviceBrand: undefined;
	get onOpenRecordsChange(): Event<void>;
	get onHighlighterChange(): Event<void>;
	getOpenRecords(): FilePathItem[];
	listFiles(root: URI): Promise<URI[]>;
	codeToHtml(code: string, language: string): Promise<TrustedHTML>;
}

export const ICodeSeekExporerService = createDecorator<ICodeSeekExporerService>('CodeSeekExporerService');

export class CodeSeekExporerService implements ICodeSeekExporerService {
	_serviceBrand: undefined;
	private model: ExplorerModel;
	private readonly disposables = new DisposableStore();
	private workspaceURIs: URI[] = [];
	private readonly _onOpenRecordsChange = new Emitter<void>();
	private readonly _onHighlighterChange = new Emitter<void>();
	private _openRecords: FilePathItem[] = [];
	private readonly storeKey: string = 'workbench.common._openRecords';
	private readonly maxStoreNum: number = 20;
	private highlighter: any;
	private themeName: string | undefined;
	constructor(
		@IFileService private fileService: IFileService,
		@IConfigurationService private configurationService: IConfigurationService,
		@IWorkspaceContextService private workspaceContextService: IWorkspaceContextService,
		@IUriIdentityService private readonly uriIdentityService: IUriIdentityService,
		@IFilesConfigurationService private readonly filesConfigurationService: IFilesConfigurationService,
		@IEditorService private readonly editorService: IEditorService,
		@IStorageService private readonly storageService: IStorageService,
		@IWorkbenchThemeService private readonly workbenchThemeService: IWorkbenchThemeService,
		@IThemeService private readonly themeService: IThemeService,
	) {
		this.model = new ExplorerModel(this.workspaceContextService, this.uriIdentityService, this.fileService, this.configurationService, this.filesConfigurationService);
		this.disposables.add(this.model);

		this._loadOpenRecords(fileService).then(() => {
			this.updateOpenRecords();
		})
		this.updateWorkspaceURIs();
		this.disposables.add(this.workspaceContextService.onDidChangeWorkspaceFolders((e) => {
			this.updateWorkspaceURIs();
		}));

		// 在事件监听中使用
		this.disposables.add(this.editorService.onDidEditorsChange(() => {
			this.updateOpenRecords();
		}));

		this.disposables.add(this.editorService.onDidCloseEditor((e) => {
			const uri = e.editor.resource;
			if (uri) {
				this.updateOpenRecord({
					isDirectory: false,
					relativePath: this.getRelativePath(uri),
					uri: uri,
					isActive: false,
					isOpened: false,
					hasOpened: true
				});

				const folderUri = dirname(uri);
				this.updateOpenRecord({
					isDirectory: true,
					relativePath: this.getRelativePath(folderUri),
					uri: folderUri,
					isActive: false,
					isOpened: false,
					hasOpened: true
				});
				this.storeOpenedTabs()
			}
			this._onOpenRecordsChange.fire();
		}));
		this.disposables.add(this.fileService.onDidFilesChange(e => {
			const oldLength = this._openRecords.length
			this._openRecords = this._openRecords.filter(item => !e.contains(item.uri, FileChangeType.DELETED));
			if (oldLength > this._openRecords.length) {
				this.storeOpenedTabs()
				this._onOpenRecordsChange.fire();
			}
		}))

		// 监听主题变化，重新初始化highlighter
		this.disposables.add(this.themeService.onDidColorThemeChange(async () => {
		await this.initHighlighter();
		 this._onHighlighterChange.fire();
	}));
	}

	private async initHighlighter() {
		const themeData = await this.workbenchThemeService.getRawThemeContent();
		if (!themeData?.type) {
			const themeType = this.themeService.getColorTheme().type;
			themeData.type = themeType;
		}
		this.themeName = themeData?.name;
		this.highlighter = await createHighlighter({
			themes: [themeData],
			langs: Object.keys(bundledLanguages),
		});
	}

	public async codeToHtml(code: string, language: string): Promise<TrustedHTML> {
		if (!this.highlighter) {
			await this.initHighlighter();
		}
		const html = this.highlighter.codeToHtml(code, {
			lang: language,
			theme: this.themeName,
			transformers: [
				{
					pre(this: { addClassToHast: (node: Root | Element, className: string) => void }, node: Root | Element) {
						this.addClassToHast(node, 'overflow-x-auto p-2')
					}
				}
			]
		})
		const trustedHtml = DOMPurify.sanitize(html, {
			RETURN_TRUSTED_TYPE: true
		});
		return trustedHtml;
	}

	private async _loadOpenRecords(fileService: IFileService) {
		const openRecordsState = this.storageService.get(this.storeKey, StorageScope.WORKSPACE);
		const openRecords: FilePathItem[] = openRecordsState ? JSON.parse(openRecordsState).openRecords : [];
		this._openRecords = await Promise.all(openRecords.map(async item => {
			return (await fileService.exists(item.uri)) ? item : null;
		})).then(items => items.filter((item): item is FilePathItem => item !== null));
	}
	async listFiles(root: URI): Promise<URI[]> {
		const files: URI[] = [];
		const statWithMetadata = await this.fileService.resolve(root, {
			resolveMetadata: true
		})
		const children = statWithMetadata.children
		if (!children) {
			return []
		}
		for (const child of children) {
			if (!child.isDirectory) {
				files.push(child.resource);
			}
		}

		return files;
	}

	updateWorkspaceURIs(): void {
		this.workspaceURIs.length = 0;
		this.workspaceContextService.getWorkspace().folders.forEach(forlder => {
			this.workspaceURIs.push(forlder.uri);
		});
	}
	isWorkspaceFilePath(uri: URI): boolean {
		return this.model.roots.some(item => uri.path.startsWith(item.resource.path));
	}

	getRelativePath(targetUri: URI): string {
		const targetPath = targetUri.path;
		for (const workspaceURI of this.workspaceURIs) {
			const workspacePath = workspaceURI.path;
			if (targetPath.startsWith(workspacePath)) {
				return targetPath.substring(workspacePath.length + 1);
			}
		}
		return targetPath;
	}
	getOpenRecords(): FilePathItem[] {
		return this._openRecords.reverse();
	}
	get onOpenRecordsChange(): Event<void> {
		return this._onOpenRecordsChange.event;
	}
	get onHighlighterChange(): Event<void> {
		return this._onHighlighterChange.event;
	}
	updateOpenRecords(): void {
		const currentOpenedFiles = this.getAllOpenedFiles();
		currentOpenedFiles.forEach(item => {
			this.updateOpenRecord(item);
		});

		currentOpenedFiles.forEach(item => {
			const folderUri = dirname(item.uri);
			const folderItem = { isDirectory: true, relativePath: this.getRelativePath(folderUri), uri: folderUri, isActive: false, isOpened: true, hasOpened: true };
			this.updateOpenRecord(folderItem);
		});
		this._onOpenRecordsChange.fire();
	}
	updateOpenRecord(filePathItem: FilePathItem): void {
		if (filePathItem.relativePath === '' || filePathItem.relativePath === '.') {
			return;
		}
		if (this.isWorkspaceFilePath(filePathItem.uri)) {
			this._openRecords = [...this._openRecords.filter(item => item.uri.fsPath !== filePathItem.uri.fsPath), filePathItem];
		}
	}
	async traverse(item: ExplorerItem, consumer: (ele: ExplorerItem) => void): Promise<void> {
		// TODO 过滤文件做成可配置的
		if (item.isExcluded || ['.git', 'node_modules', '.idea', 'target', 'out', 'dist'].indexOf(item.name) > -1) {
			return;
		}
		consumer(item);
		const children = item.fetchChildren(SortOrder.Default);
		if (Array.isArray(children)) {

			await Promise.all(children.map(async child => {
				await this.traverse(child, consumer);
			}));
			return;
		}
		return children.then(async res => {
			await Promise.all(res.map(async child => {
				await this.traverse(child, consumer);
			}));
		});
	}
	// 获取所有打开的编辑器文件
	getAllOpenedFiles(): FilePathItem[] {
		const openedFiles = this.editorService.editors.filter(editor => editor.resource).map(editor => ({
			uri: editor.resource!,              // 文件URI
			relativePath: this.getRelativePath(editor.resource!),  // 文件路径
			isActive: editor === this.editorService.activeEditor,  // 是否是活动编辑器
			isOpened: true,
			isDirectory: false,
			hasOpened: true,
		}));

		return openedFiles;
	}

	storeOpenedTabs() {
		const stateOpneRecords = this._openRecords.length > this.maxStoreNum ? this._openRecords.slice(-1 * this.maxStoreNum) : this._openRecords;
		this.storageService.store(
			this.storeKey,
			JSON.stringify({ openRecords: stateOpneRecords }),
			StorageScope.WORKSPACE,
			StorageTarget.USER
		)
	}
}

registerSingleton(ICodeSeekExporerService, CodeSeekExporerService, InstantiationType.Eager);
