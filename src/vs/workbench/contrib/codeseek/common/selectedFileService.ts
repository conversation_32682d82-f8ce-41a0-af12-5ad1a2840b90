import { URI } from '../../../../base/common/uri.js';
import { IRange } from '../../../../editor/common/core/range.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { IRequestService } from '../../../../platform/request/common/request.js';
import { request } from '../electron-main/uac/utilities/request.js';
import { ICodeseekUacLoginService } from './uac/UacloginTypes.js';

// one of the square items that indicates a selection in a chat bubble (NOT a file, a Selection of text)
export type CodeSelection = {
	type: 'Selection';
	fileURI: URI;
	title: string;
	selectionStr: string;
	range: IRange;
	fromMention: boolean;
};

export type FileSelection = {
	type: 'File';
	fileURI: URI;
	title: string;
	selectionStr: null;
	range: null;
	fromActive: boolean;
	fromMention: boolean;
	fromEditor: boolean;
};

export type FolderSelection = {
	type: 'Folder';
	fileURI: URI;
	title: string;
	selectionStr: null;
	range: null;
	fromMention: boolean;
};

export type CodebaseSelection = {
	type: 'Codebase';
	fileURI: URI;
	title: string;
	selectionStr: string;
	range: IRange;
	fromMention: boolean;
};

export type TerminalSelection = {
	type: 'Terminal';
	fileURI: URI;
	title: string;
	content: string;
	selectionStr: string;
	range: null;
	timestamp: number;
	fromMention: boolean;
};

export type UrlSelection = {
	type: "Url";
	fileURI: URI;
	title: string;
	selectionStr: null;
	range: null;
	fromMention: true;
}

export type ICenterSelection = {
	type: "ICenter";
	fileURI: URI;
	title: string;
	selectionStr: null;
	range: null;
	fromMention: true;
}

export type StagingSelectionItem = CodeSelection | FileSelection | FolderSelection | CodebaseSelection | TerminalSelection | UrlSelection | ICenterSelection;

export type CodeBaseQuery = {
	query: string;
	project: string;
	needSummary: boolean;
	stream: boolean;
};

export interface IMentionService {
	readonly _serviceBrand: undefined;
	addItemToSelectedFile(selections: StagingSelectionItem[] | undefined, selection: StagingSelectionItem): StagingSelectionItem[];
	newUrlMentionSelection(url: string): Promise<UrlSelection | ICenterSelection>;
}



export const IMentionService = createDecorator<IMentionService>('MentionService');

class MentionService implements IMentionService {
	readonly _serviceBrand: undefined;
	constructor(
		@IRequestService private readonly requestService: IRequestService,
		@ICodeseekUacLoginService private readonly uacloginService: ICodeseekUacLoginService,
		@INotificationService private readonly notificationService: INotificationService,
	) {

	}

	addItemToSelectedFile(selections: StagingSelectionItem[] | undefined, selection: StagingSelectionItem): StagingSelectionItem[] {
		// if matches with existing selection, overwrite (since text may change)
		const matchingStagingEltIdx = this.findMatchingStagingIndex(selections, selection);
		if (matchingStagingEltIdx !== undefined && matchingStagingEltIdx !== -1) {
			if (selections) {
				const oldSelection = selections?.[matchingStagingEltIdx];
				selection.fromMention = oldSelection?.fromMention && selection.fromMention;
			}
			return [
				...selections!.slice(0, matchingStagingEltIdx),
				selection,
				...selections!.slice(matchingStagingEltIdx + 1, Infinity)
			];
		}
		// if no match, add it
		else {
			return [...(selections ?? []), selection];
		}
	}


	findMatchingStagingIndex(currentSelections: StagingSelectionItem[] | undefined, newSelection: StagingSelectionItem) {
		return currentSelections?.findIndex(s => {
			// 对于终端选择，根据类型和内容进行匹配
			if (s.type === 'Terminal' && newSelection.type === 'Terminal') {
				return (s as TerminalSelection).content === (newSelection as TerminalSelection).content;
			}

			// 对于其他类型的选择，保持原有的匹配逻辑
			if ('fileURI' in s && 'fileURI' in newSelection && 'range' in s && 'range' in newSelection) {
				return s.fileURI?.toString(true) === newSelection.fileURI?.toString(true)
					&& s.range?.startLineNumber === newSelection.range?.startLineNumber
					&& s.range?.endLineNumber === newSelection.range?.endLineNumber;
			}

			return false;
		});
	}
	async newUrlMentionSelection(url: string): Promise<UrlSelection | ICenterSelection> {
		const uri = URI.parse(url)
		if (this.isICenterLink(uri)) {
			const title = await this.queryIcenterLinkTitle(uri)
			return {
				type: "ICenter",
				title,
				fileURI: uri,
				selectionStr: null,
				range: null,
				fromMention: true,
			}
		} else {
			return {
				type: "Url",
				fileURI: uri,
				title: url,
				selectionStr: null,
				range: null,
				fromMention: true,
			}
		}
	}

	isICenterLink(uri: URI) {
		return uri.authority === "i.zte.com.cn"
	}


	async queryIcenterLinkTitle(uri: URI): Promise<string> {
		const fragmentChip = uri.fragment.split('/')
		if (fragmentChip.length <= 2) {
			this.notificationService.error(`引用路径不是正确的Icenter页面路径`)
			return uri.fsPath;
		}

		const pageId = fragmentChip[fragmentChip.length - 2]
		const queryUrl = `https://icenterapi.zte.com.cn/zte-rd-icenter-contents/content/title/${pageId}`
		const userInfo = await this.uacloginService.getUserInfo()
		if (userInfo === undefined) {
			return uri.path
		}
		const options = {
			url: queryUrl,
			type: 'GET',
			json: true,
			headers: {
				'X-Emp-No': userInfo.userId,
				'X-Auth-Value': userInfo.token,
			}
		}
		return new Promise((resolve, reject) => {
			request<QueryTiTleBody>(this.requestService, options, (error, response, body) => {
				if (body && body.code.code === "0000") {
					resolve(body.bo.title)
				} else {
					this.notificationService.error(`访问Icenter页面出错, ${body?.code.msg}`)
					resolve(uri.toString(true))
				}
			})
		})
	}
}


interface Bo {
	archiveStatus: number; // 归档状态（0: 未归档）
	contentBody: string; // 内容主体
	encrypted: boolean; // 是否加密
	id: string; // 唯一标识
	parentId: string; // 父级 ID
	parentPath: string; // 父级路径
	spaceId: string; // 空间 ID
	spaceType: number; // 空间类型（0: 未知类型，需根据业务场景补充具体枚举值）
	title: string; // 标题（知识库描述）
}

interface Code {
	code: string; // 返回码（"0000" 表示成功）
	msg: string; // 操作结果信息
	msgId: string; // 消息 ID（成功标识）
}

interface QueryTiTleBody {
	bo: Bo;
	code: Code;
}

registerSingleton(IMentionService, MentionService, InstantiationType.Eager);
