/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { ProxyChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { Event } from '../../../../base/common/event.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { INotificationService, Severity } from '../../../../platform/notification/common/notification.js';
import { IHostService } from '../../../services/host/browser/host.js';
import { IContextKeyService, RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';
import { IMetricsService, METRICS_EVENT } from './metricsService.js';

export const FLOW_HAS_UPDATE_AVAILABLE = new RawContextKey<boolean>('flow.hasUpdateAvailable', false);

export interface UpgradeProgress {
	receivedBytes: number;
	totalBytes: number;
	percentage: number;
	stage: 'download' | 'install';
	message: string;
}

export interface ICodeseekUpdateService {
	readonly _serviceBrand: undefined;
	readonly onProgress: Event<UpgradeProgress>;
	check: () => Promise<{ hasUpdate: true; message: string, lastVersion: string } | { hasUpdate: false } | null>;
	install: () => Promise<void>;
}

export const ICodeseekUpdateService = createDecorator<ICodeseekUpdateService>('CodeseekUpdateService');

// implemented by calling channel
export class CodeseekUpdateService extends Disposable implements ICodeseekUpdateService {
	readonly _serviceBrand: undefined;
	readonly onProgress: Event<UpgradeProgress>;
	private readonly codeseekUpdateService: ICodeseekUpdateService;
	private progressNotification: any = null;



	constructor(
		@IMainProcessService mainProcessService: IMainProcessService,
		@INotificationService private readonly notifService: INotificationService,
		@IHostService private readonly hostService: IHostService,
		@IContextKeyService private readonly contextKeyService: IContextKeyService,
		@IMetricsService private readonly metricsService: IMetricsService,
	) {
		super();
		this.codeseekUpdateService = ProxyChannel.toService<ICodeseekUpdateService>(mainProcessService.getChannel('codeseek-channel-update'));
		this.onProgress = this.codeseekUpdateService.onProgress;
	}

	public async update(isNeedPrompt: boolean = true): Promise<void> {
		const result = await this.check();

		if (!result) {
			this.notifyError();
			return;
		}

		if (result.hasUpdate) {
			this.updateContext(true);
			if (isNeedPrompt) {
				this.callInstall(result.message);
			}
		} else {
			this.updateContext(false);
		}
	}

	public updateContext(hasUpdate: boolean): void {
		const updateAvailableKey = FLOW_HAS_UPDATE_AVAILABLE.bindTo(this.contextKeyService);
		updateAvailableKey.set(hasUpdate);
	}

	notifyError(): void {
		const message = `错误: 检查更新时发生错误。如果问题持续存在, 请联系我们或重新安装Flow [点击这里](https://artxa.zte.com.cn/artifactory/rjgctd-local-release-generic/ai-ide)！`;
		this.notifService.notify({
			severity: Severity.Info,
			message: message,
		});
	}

	showDownloadProgress(progress: UpgradeProgress): void {
		let message = '';
		if (progress.stage === 'download') {
			message = `正在下载更新: ${progress.percentage}% (${this.formatBytes(progress.receivedBytes)}/${this.formatBytes(progress.totalBytes)})`;
		} else {
			message = progress.message;
		}

		if (this.progressNotification) {
			this.progressNotification.updateMessage(message);
		} else {
			this.progressNotification = this.notifService.notify({
				severity: Severity.Info,
				message: message,
				sticky: true,
				neverShowAgain: { id: 'flow.update.progress' }
			});
		}
	}

	private formatBytes(bytes: number): string {
		if (bytes === 0) return '0 B';
		const k = 1024;
		const sizes = ['B', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	public async callInstall(message?: string): Promise<void> {
		this.notifService.prompt(
			Severity.Info,
			message || '发现新版本可用，是否立即更新？',
			[
				{
					label: '立即更新',
					run: async () => {
						await this.install_();
						this.metricsService.capture(METRICS_EVENT.UPGRADE, { upgradeVersion: message || 'unknown' });
					},
				},
				{
					label: '稍后再说',
					run: () => { }
				}
			],
			{
				neverShowAgain: { id: 'flow.update.prompt' }
			}
		);
	}

	public async install_(): Promise<void> {
		try {
			// 清除之前的进度通知
			if (this.progressNotification) {
				this.progressNotification.close();
				this.progressNotification = null;
			}

			// 监听进度事件
			const disposable = this.onProgress(progress => {
				this.showDownloadProgress(progress);
			});

			await this.install();

			// 安装后关闭进度通知
			if (this.progressNotification) {
				this.progressNotification.close();
				this.progressNotification = null;
			}
			this.updateContext(false);

			// 清理事件监听
			disposable.dispose();

			// 设置10秒后自动重启
			setTimeout(async () => {
				try {
					// 显示重启进度通知
					this.progressNotification = this.notifService.notify({
						severity: Severity.Info,
						message: '正在重启所有Flow实例...',
						sticky: true
					});

					await this.hostService.restart();
				} catch (error) {
					this.notifService.notify({
						severity: Severity.Error,
						message: `自动重启失败: ${error.message}, 请手动重启所有Flow实例`
					});
				}
			}, 10000);

			this.notifService.notify({
				severity: Severity.Info,
				message: '更新已完成, 将在10秒后自动重启',
				sticky: true
			});
		} catch (error) {
			// 发生错误时关闭进度通知
			if (this.progressNotification) {
				this.progressNotification.close();
				this.progressNotification = null;
			}

			this.notifService.notify({
				severity: Severity.Error,
				message: `更新失败: ${error.message}`
			});
		}
	}

	check: ICodeseekUpdateService['check'] = async () => {
		const res = await this.codeseekUpdateService.check();
		return res;
	};

	install: ICodeseekUpdateService['install'] = async () => {
		const res = await this.codeseekUpdateService.install();
		return res;
	};
}

registerSingleton(ICodeseekUpdateService, CodeseekUpdateService, InstantiationType.Eager);


