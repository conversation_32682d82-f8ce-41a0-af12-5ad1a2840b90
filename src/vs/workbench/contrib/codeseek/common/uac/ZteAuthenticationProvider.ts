import { Emitter, Event } from '../../../../../base/common/event.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { IContextKey, IContextKeyService, RawContextKey } from '../../../../../platform/contextkey/common/contextkey.js';
import { IAuthenticationProvider, AuthenticationSession, AuthenticationSessionsChangeEvent, IAuthenticationProviderSessionOptions } from '../../../../services/authentication/common/authentication.js';
import { CODESEEK_OPEN_UAC_LOGIN_ACTION_ID } from '../../browser/codeseekSettingsUacLogin.js';
import { ICodeseekUacLoginService, UserInfo, UserStatus } from './UacloginTypes.js';


export const ZTE_USER_LOGIN_STATE_KEY = 'zteUserLogin';
export const ZTE_USER_LOGIN_STATE = new RawContextKey<UserStatus>(ZTE_USER_LOGIN_STATE_KEY, UserStatus.logout);

export class ZteAuthenticationProvider implements IAuthenticationProvider {
	readonly id: string = 'ZTE';
	readonly label: string = 'ZTE';
	readonly supportsMultipleAccounts: boolean = true;
	readonly onDidChangeSessions: Event<AuthenticationSessionsChangeEvent>;
	private readonly _onDidChangeSessions = new Emitter<AuthenticationSessionsChangeEvent>;

	private authenticationSession?: AuthenticationSession;


	private readonly loginStateContext: IContextKey<UserStatus>;
	constructor(
		@ICodeseekUacLoginService private readonly codeseekUacLoginService: ICodeseekUacLoginService,
		@ICommandService private commandService: ICommandService,
		@IContextKeyService readonly contextKeyService: IContextKeyService,
	) {
		this.onDidChangeSessions = this._onDidChangeSessions.event;

		codeseekUacLoginService.onDidUserLogin((userInfo) => {
			this.sessionChange(userInfo)
			this.loginStateContext.set(UserStatus.login)
		})
		codeseekUacLoginService.onDidUserLogout((userInfo) => {
			this.sessionChange(userInfo)
			this.loginStateContext.set(UserStatus.logout)
		})
		codeseekUacLoginService.onDidChangeUserInfo((userInfo) => {
			this.loginStateContext.set(userInfo.userStatus || UserStatus.logout)
			this.sessionChange(userInfo);
		})
		codeseekUacLoginService.getUserInfo().then(userInfo => {
			if (userInfo) {
				this.sessionChange(userInfo)
				this.loginStateContext.set(userInfo.userStatus || UserStatus.logout)
			}
		})

		this.loginStateContext = ZTE_USER_LOGIN_STATE.bindTo(contextKeyService);
	}

	getSessions(scopes: string[] | undefined, options: IAuthenticationProviderSessionOptions): Promise<readonly AuthenticationSession[]> {
		const targetSesstions: AuthenticationSession[] = this.authenticationSession ? [this.authenticationSession] : [];
		return Promise.resolve(targetSesstions);
	}

	createSession(scopes: string[], options: IAuthenticationProviderSessionOptions): Promise<AuthenticationSession> {
		if (this.authenticationSession) {
			return Promise.resolve(this.authenticationSession);
		} else {
			this.commandService.executeCommand(CODESEEK_OPEN_UAC_LOGIN_ACTION_ID)
		}
		return Promise.resolve({} as AuthenticationSession);
	}

	removeSession(sessionId: string): Promise<void> {
		this.codeseekUacLoginService.logout();
		return Promise.resolve();
	}

	private sessionChange(userInfo: UserInfo) {
		const authenticationSession: AuthenticationSession = {
			id: userInfo.userId || '',
			accessToken: userInfo.token || '',
			account: {
				id: this.id, label: userInfo.username || ''
			},
			scopes: [],
			idToken: userInfo.token,
		}

		if (userInfo.userStatus == UserStatus.logout) {
			this.authenticationSession = undefined;
		} else {
			this.authenticationSession = authenticationSession;
		}

		const authenticationSessionsChangeEvent: AuthenticationSessionsChangeEvent = {
			added: userInfo.userStatus === UserStatus.login ? [authenticationSession] : undefined,
			removed: userInfo.userStatus === UserStatus.logout ? [authenticationSession] : undefined,
			changed: userInfo.userStatus === UserStatus.change ? [authenticationSession] : undefined,
		}
		this._onDidChangeSessions.fire(authenticationSessionsChangeEvent)
	}
}
