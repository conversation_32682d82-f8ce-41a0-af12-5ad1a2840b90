/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../../base/common/lifecycle.js';
import { registerSingleton, InstantiationType } from '../../../../../platform/instantiation/common/extensions.js';


import { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';
import { ZTE_USER_INFO_KEY } from '../../common/storageKeys.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../../platform/storage/common/storage.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { CODESEEK_OPEN_UAC_LOGIN_ACTION_ID } from '../../browser/codeseekSettingsUacLogin.js';
import { IMainProcessService } from '../../../../../platform/ipc/common/mainProcessService.js';
import { IChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { ZTE_USER_LOGIN_CHANNEL_NAME, ZteUserLoginChannelCommand } from './zteUserTypes.js';
import { Emitter, Event } from '../../../../../base/common/event.js';
import { CancellationToken } from '../../../../../base/common/cancellation.js';

import { UserInfo, UserStatus, ICodeseekUacLoginService, AuthInfo } from './UacloginTypes.js';
import { IAuthenticationService } from '../../../../services/authentication/common/authentication.js';
import { ZteAuthenticationProvider } from './ZteAuthenticationProvider.js';





class CodeseekUacLoginService extends Disposable implements ICodeseekUacLoginService {
	_serviceBrand: undefined;

	private readonly http_proxy_key = 'http.proxy';
	private readonly https_proxy_key = 'https.proxy';
	private readonly _onDidChangeUserInfo = new Emitter<UserInfo>();
	private readonly _onDidUserLogin = new Emitter<UserInfo>();
	private readonly _onDidUserLogout = new Emitter<UserInfo>;

	onDidChangeUserInfo: Event<UserInfo> = this._onDidChangeUserInfo.event;
	onDidUserLogin: Event<UserInfo> = this._onDidUserLogin.event;
	onDidUserLogout: Event<UserInfo> = this._onDidUserLogout.event;
	private userInfo: UserInfo | undefined;
	private channel: IChannel;


	constructor(
		@IConfigurationService private readonly _configurationService: IConfigurationService,
		@INotificationService private readonly notificationService: INotificationService,
		@ICommandService private readonly commandService: ICommandService,
		@IStorageService private readonly storageService: IStorageService,
		@IMainProcessService readonly mainProcessService: IMainProcessService,
		@IAuthenticationService readonly authenticationService: IAuthenticationService,
		@IInstantiationService readonly instantiationService: IInstantiationService,
	) {
		super();
		this.channel = mainProcessService.getChannel(ZTE_USER_LOGIN_CHANNEL_NAME);

		const kaTimer = setInterval(() => {
			const zteAuthenticationProvider = instantiationService.createInstance(ZteAuthenticationProvider);
			authenticationService.registerAuthenticationProvider("ZTE", zteAuthenticationProvider)
			this.refreshUserSession()
			clearInterval(kaTimer)
		}, 2000);
	}


	private succLoginFunc(result: any) {
		const userInfo = { userId: result.userId, username: result.username, department: result.department, userStatus: UserStatus.login, token: result.token, gerritHttpPassword: result.gerritHttpPassword, trackInfo: result.trackInfo };
		this.setUserInfo(userInfo)

		this._onDidUserLogin.fire(userInfo);
		this.notificationService.info(`${userInfo.username}已成功登录！`);
	}

	public async udsLogin(): Promise<void> {
		const result: any = await this.channel.call(ZteUserLoginChannelCommand.UDS_PWQ_FREE)
		if (result.error) {
			this.notificationService.error(`${result.error}`);
		} else {
			this.succLoginFunc(result);
		}

	}

	public async codeServerLogin(cookies: string): Promise<void> {
		const result: any = await this.channel.call(ZteUserLoginChannelCommand.CODE_SERVER_PWD_PREE, [cookies])

		if (result.error) {
			this.notificationService.error(`${result.error}`);
		} else {
			this.succLoginFunc(result);
		}
	}

	public async qrLogin(dataStr: string, cancellationToken: CancellationToken): Promise<void> {
		try {
			const result: any = await this.channel.call(ZteUserLoginChannelCommand.POOL_UAC, [dataStr], cancellationToken)

			if (result.error) {
				this.notificationService.error(`${result.error}`);
			} else {
				this.succLoginFunc(result);
			}
		} catch (e) {
			if (e.name !== 'Canceled') {
				this.notificationService.error(`${e}`)
			}
		}
	}

	public async login(authInfo: AuthInfo): Promise<boolean> {
		const result: any = await this.channel.call(ZteUserLoginChannelCommand.LOGIN_WITH_PWD, [authInfo.userId, authInfo.password, authInfo.dynPwd])

		if (result.error) {
			this.notificationService.error(`${result.error}`);
			return false;
		} else {
			this.succLoginFunc(result);
			return true
		}
	}

	public async logout(): Promise<void> {
		if (this.userInfo) {

			this.userInfo.userStatus = UserStatus.logout
			this.setUserInfo(this.userInfo)

			this._onDidUserLogout.fire(this.userInfo)
			this.notificationService.info("您已注销登录！");
		}
	}

	public async getUserInfo(): Promise<UserInfo | undefined> {
		await this.refreshUserSession()
		return this.userInfo;
	}

	public setUserInfo(userInfo: UserInfo) {
		if (this.isUserInfoEqual(this.userInfo, userInfo)) {
			return
		}
		this.userInfo = userInfo;

		this._onDidChangeUserInfo.fire(this.userInfo)

		this.saveUserInfo(userInfo)

	}

	private async refreshUserSession() {
		const userSessionInfo: UserInfo | undefined = this.loadUserInfo();

		if (userSessionInfo && userSessionInfo.userId && userSessionInfo.token && userSessionInfo.userStatus == UserStatus.login) {
			try {
				await this.channel.call(ZteUserLoginChannelCommand.QUERY_USER_INFO, [userSessionInfo.userId, userSessionInfo.token])
				this.setUserInfo(userSessionInfo);
			} catch (error) {
				this.removeUserInfo()
				this.notificationService.info("会话失效，请重新登录！");
				await this.openUacLogin();
			}
		} else {
			await this.openUacLogin();
		}

	}

	private async openUacLogin() {
		this.commandService.executeCommand(CODESEEK_OPEN_UAC_LOGIN_ACTION_ID);
	}

	public async check() {
		const httpProxy = this._configurationService.getValue<string>(this.http_proxy_key);
		const httpsProxy = this._configurationService.getValue<string>(this.https_proxy_key)
		const results: string[] = await this.channel.call(ZteUserLoginChannelCommand.PROMBLES_CHECK, [httpProxy, httpsProxy])
		return results;
	}
	private saveUserInfo(userInfo: UserInfo) {
		this.storageService.store(ZTE_USER_INFO_KEY, JSON.stringify(userInfo), StorageScope.APPLICATION, StorageTarget.USER)
	}
	private loadUserInfo() {
		const userInfoStr = this.storageService.get(ZTE_USER_INFO_KEY, StorageScope.APPLICATION)
		if (userInfoStr) {
			return JSON.parse(userInfoStr);
		}
	}

	private removeUserInfo() {
		this.storageService.remove(ZTE_USER_INFO_KEY, StorageScope.APPLICATION);
	}

	private isUserInfoEqual(oldUserInfo: UserInfo | undefined, newUserInfo: UserInfo | undefined): boolean {
		if (oldUserInfo === newUserInfo) return true; // 如果两个引用相同
		if (!oldUserInfo || !newUserInfo) return false; // 如果其中一个是 undefined

		return oldUserInfo.userId === newUserInfo.userId &&
			oldUserInfo.username === newUserInfo.username &&
			oldUserInfo.department === newUserInfo.department &&
			oldUserInfo.userStatus === newUserInfo.userStatus &&
			oldUserInfo.token === newUserInfo.token &&
			oldUserInfo.gerritHttpPassword === newUserInfo.gerritHttpPassword &&
			this.isTrackInfoEqual(oldUserInfo.trackInfo, newUserInfo.trackInfo);
	}

	private isTrackInfoEqual(oldTrackInfo?: { department?: string; id?: string; workplace?: string; }, newTrackInfo?: { department?: string; id?: string; workplace?: string; }): boolean {
		if (oldTrackInfo === newTrackInfo) return true; // 如果两个引用相同
		if (!oldTrackInfo || !newTrackInfo) return false; // 如果其中一个是 undefined

		return oldTrackInfo.department === newTrackInfo.department &&
			oldTrackInfo.id === newTrackInfo.id &&
			oldTrackInfo.workplace === newTrackInfo.workplace;
	}
}


registerSingleton(ICodeseekUacLoginService, CodeseekUacLoginService, InstantiationType.Eager);
