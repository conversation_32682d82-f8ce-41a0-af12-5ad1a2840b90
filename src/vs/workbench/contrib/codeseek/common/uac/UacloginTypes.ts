


import { CancellationToken } from '../../../../../base/common/cancellation.js';
import { Event } from '../../../../../base/common/event.js';
import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
export enum UserStatus {
	login = 1,
	logout = 2,
	change = 3,
}

export interface UserInfo {
	userId?: string;
	username?: string;
	department?: string;
	userStatus?: UserStatus;
	token?: string;
	gerritHttpPassword?: string;
	trackInfo?: {
		department?: string;
		id?: string;
		workplace?: string;
	};
}
export type AuthInfo = {
	userId: string;
	password: string;
	dynPwd: string;
}

export const ICodeseekUacLoginService = createDecorator<ICodeseekUacLoginService>('CodeseekUacLoginService');
export interface ICodeseekUacLoginService {
	readonly _serviceBrand: undefined;
	udsLogin(): Promise<void>;
	codeServerLogin(cookie: string): Promise<void>;
	getUserInfo(): Promise<UserInfo | undefined>;
	logout(): Promise<void>
	login(authInfo: AuthInfo): Promise<boolean>;
	qrLogin(dataStr: string, cancellationToken: CancellationToken): Promise<void>;
	check(): Promise<string[]>;
	onDidChangeUserInfo: Event<UserInfo>;
	onDidUserLogin: Event<UserInfo>;
	onDidUserLogout: Event<UserInfo>;
}


