/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { ChatMode, CodeseekSettingsState } from './codeseekSettingsService.js';


type UnionOfKeys<T> = T extends T ? keyof T : never;

export enum ProviderNames {
	default = 'default',
	openAICompatible = 'openAICompatible',
	// saturn = 'saturn',
	// openAI = 'openAI',
	// deepseek = 'deepseek',
	// anthropic = 'anthropic',
	// ollama = 'ollama',
	// vLLM = 'vLLM',
	// openRouter = 'openRouter',
	// gemini = 'gemini',
	// groq = 'groq',
	// xAI = 'xAI',
}

export type defaultProviderSettingsType = {
	title: string;
	baseURL: string;
	apiKey: string;
	models: string[];
	applyModels: string[];
	placeholder: string | string[];
	subTextMd: string;
	endpoint?: string | undefined;
};

export const defaultProviderSettings = {
	[ProviderNames.default]: {
		title: 'Default',
		baseURL: 'http://10.55.58.14:30804/test-service-2/v1/v1',
		apiKey: '',
		models: [
			'AI-IDE-Chat-32B',
			'AI-IDE-R1-32B',
			'AI-IDE-FastApply'
		],
		applyModels: [
			'AI-IDE-FastApply'
		],
		placeholder: '',
		subTextMd: '',
	},
	// [ProviderNames.saturn]: {
	// 	title: 'Saturn',
	// 	baseURL: 'http://nebulacoder.dev.zte.com.cn:40081/v1',
	// 	endpoint: '',
	// 	apiKey: 'RAN-LL-23cb52a9-41d8-46e5-9b20-75c4f3805b4d',
	// 	models: [
	// 		'NTele-72B-V2',
	// 		'nebulacoder-v6.0',
	// 	],
	// 	applyModels: [],
	// 	placeholder: '',
	// 	subTextMd: '',
	// },
	// [ProviderNames.openAI]: {
	// 	title: 'OpenAI',
	// 	baseURL: 'https://api.openai.com/v1',
	// 	apiKey: '',
	// 	models: [
	// 		'o1',
	// 		'o3-mini',
	// 		'o1-mini',
	// 	],
	// 	applyModels: [],
	// 	placeholder: 'sk-proj-key...',
	// 	subTextMd: 'Get your [API Key here](https://platform.openai.com/api-keys).',
	// },
	// [ProviderNames.deepseek]: {
	// 	title: 'DeepSeek',
	// 	baseURL: 'https://api.deepseek.com/v1',
	// 	apiKey: '',
	// 	models: [
	// 		'deepseek-chat',
	// 		'deepseek-reasoner',
	// 	],
	// 	applyModels: [],
	// 	placeholder: 'sk-key...',
	// 	subTextMd: 'Get your [API Key here](https://platform.deepseek.com/api_keys).',
	// },
	[ProviderNames.openAICompatible]: {
		title: 'OpenAI Compatible',
		baseURL: '',
		endpoint: '',
		apiKey: '',
		models: [],
		applyModels: [],
		placeholder: ['https://my-website.com/v1', 'sk-key...'],
		subTextMd: '',
	},
	// [ProviderNames.anthropic]: {
	// 	title: 'Anthropic',
	// 	baseURL: '',
	// 	apiKey: '',
	// 	models: [
	// 		'claude-3-5-sonnet-latest',
	// 		'claude-3-5-haiku-latest',
	// 		'claude-3-opus-latest',
	// 	],
	// 	applyModels: [],
	// 	placeholder: 'sk-ant-key...',
	// 	subTextMd: 'Get your [API Key here](https://console.anthropic.com/settings/keys).',
	// },
	// [ProviderNames.ollama]: {
	// 	title: 'Ollama',
	// 	baseURL: '',
	// 	endpoint: 'http://127.0.0.1:11434',
	// 	models: [],
	// 	applyModels: [],
	// 	placeholder: 'http://127.0.0.1:11434',
	// 	subTextMd: 'If you would like to change this endpoint, please read more about [Endpoints here](https://github.com/ollama/ollama/blob/main/docs/faq.md#how-can-i-expose-ollama-on-my-network).',
	// },
	// [ProviderNames.vLLM]: {
	// 	title: 'vLLM',
	// 	baseURL: '',
	// 	endpoint: 'http://localhost:8000',
	// 	models: [],
	// 	applyModels: [],
	// 	placeholder: 'http://localhost:8000',
	// 	subTextMd: 'If you would like to change this endpoint, please read more about [Endpoints here](https://docs.vllm.ai/en/latest/getting_started/quickstart.html#openai-compatible-server).',
	// },
	// [ProviderNames.openRouter]: {
	// 	title: 'OpenRouter',
	// 	baseURL: 'https://openrouter.ai/api/v1',
	// 	apiKey: '',
	// 	models: [
	// 		'anthropic/claude-3.5-sonnet',
	// 		'deepseek/deepseek-r1',
	// 		'mistralai/codestral-2501',
	// 		'qwen/qwen-2.5-coder-32b-instruct',
	// 	],
	// 	applyModels: [],
	// 	placeholder: 'sk-or-key...',
	// 	subTextMd: 'Get your [API Key here](https://openrouter.ai/settings/keys).',
	// },
	// [ProviderNames.gemini]: {
	// 	title: 'Gemini',
	// 	baseURL: 'https://generativelanguage.googleapis.com/v1beta/openai',
	// 	apiKey: '',
	// 	models: [
	// 		'gemini-2.0-flash',
	// 		'gemini-1.5-flash',
	// 		'gemini-1.5-pro',
	// 		'gemini-1.5-flash-8b',
	// 		'gemini-2.0-flash-thinking-exp',
	// 	],
	// 	applyModels: [],
	// 	placeholder: 'sk-key...',
	// 	subTextMd: 'Get your [API Key here](https://aistudio.google.com/apikey).',
	// },
	// [ProviderNames.groq]: {
	// 	title: 'Groq',
	// 	baseURL: 'https://api.groq.com/openai/v1',
	// 	apiKey: '',
	// 	models: [
	// 		'llama-3.3-70b-versatile',
	// 		'llama-3.1-8b-instant',
	// 		'qwen-2.5-coder-32b',
	// 	],
	// 	applyModels: [],
	// 	placeholder: 'gsk_key...',
	// 	subTextMd: 'Get your [API Key here](https://console.groq.com/keys).',
	// },
	// [ProviderNames.xAI]: {
	// 	title: 'xAI',
	// 	baseURL: 'https://api.x.ai/v1',
	// 	apiKey: '',
	// 	models: [
	// 		'grok-2-latest',
	// 		'grok-3-latest',
	// 	],
	// 	applyModels: [],
	// 	placeholder: 'xai-key...',
	// 	subTextMd: 'Get your [API Key here](https://console.x.ai).',
	// },
} as Record<ProviderNames, defaultProviderSettingsType>;


export const defaultModelsOfProvider: Record<ProviderNames, { models: string[]; applyModels: string[] }> = Object.fromEntries(
	Object.entries(defaultProviderSettings).map(([providerName, provider]) =>
		[providerName, { models: [...provider.models], applyModels: [...provider.applyModels] }]
	)
) as Record<ProviderNames, { models: string[]; applyModels: string[] }>;

export const providerNames = Object.keys(defaultProviderSettings) as ProviderNames[];

export const localProviderNames = [] satisfies ProviderNames[]; // all local names
export const nonlocalProviderNames = providerNames.filter((name) => !(localProviderNames as string[]).includes(name)); // all non-local names

type CustomSettingName = Exclude<UnionOfKeys<typeof defaultProviderSettings[ProviderNames]>, 'title' | 'models' | 'placeholder' | 'subTextMd' | 'applyModels'>;
type CustomProviderSettings<providerName extends ProviderNames> = {
	[k in CustomSettingName]: k extends keyof typeof defaultProviderSettings[providerName] ? (typeof defaultProviderSettings[providerName][k]) : undefined
};
export const customSettingNamesOfProvider = (providerName: ProviderNames) => {
	const provider = defaultProviderSettings[providerName];
	const allKeys = Object.keys(provider);
	return allKeys.filter(key => key === 'apiKey' || key === 'endpoint') as CustomSettingName[];
};



export type CodeseekModelInfo = { // <-- STATEFUL
	modelName: string;
	isSupportConfig: boolean; // whether or not it's a default for its provider
	isHidden: boolean; // whether or not the user is hiding it (switched off)
	isAutodetected?: boolean; // whether the model was autodetected by polling
	isApplyModel: boolean; // whether or not it's an apply model
};  // TODO!!! eventually we'd want to let the user change supportsFIM, etc on the model themselves



type CommonProviderSettings = {
	isDefault: boolean;
	_didFillInProviderSettings: boolean | undefined; // undefined initially, computed when user types in all fields
	models: CodeseekModelInfo[];
};

export type SettingsAtProvider<providerName extends ProviderNames> = CustomProviderSettings<providerName> & CommonProviderSettings;

// part of state
export type SettingsOfProvider = {
	[providerName in ProviderNames]: SettingsAtProvider<providerName>
};


export type SettingName = keyof SettingsAtProvider<ProviderNames>;

type DisplayInfoForProviderName = {
	title: string;
	desc?: string;
};

export const displayInfoOfProviderName = (providerName: ProviderNames): DisplayInfoForProviderName => {
	return {
		title: defaultProviderSettings[providerName].title,
	};
};

type DisplayInfo = {
	title: string;
	placeholder: string;
	subTextMd?: string;
	isPasswordField?: boolean;
};

export const displayInfoOfSettingName = (providerName: ProviderNames, settingName: SettingName): DisplayInfo | undefined => {
	if (settingName === 'apiKey') {
		return {
			title: 'API Key',

			// **Please follow this convention**:
			// The word "key..." here is a placeholder for the hash. For example, sk-ant-key... means the key will look like sk-ant-abcdefg123...
			placeholder: Array.isArray(defaultProviderSettings[providerName].placeholder) ? defaultProviderSettings[providerName].placeholder[1] : defaultProviderSettings[providerName].placeholder ?? '',

			subTextMd: defaultProviderSettings[providerName].subTextMd ?? '',
			isPasswordField: true,
		};
	}
	else if (settingName as string === 'endpoint') {
		return {
			title: providerName as string === 'ollama' ? 'Endpoint' :
				providerName as string === 'vLLM' ? 'Endpoint' :
					providerName as string === 'openAICompatible' ? 'baseURL' : // (do not include /chat/completions)
						'(never)',

			placeholder: Array.isArray(defaultProviderSettings[providerName].placeholder) ? defaultProviderSettings[providerName].placeholder[0] : defaultProviderSettings[providerName].placeholder ?? '(never)',

			subTextMd: defaultProviderSettings[providerName].subTextMd ?? undefined,
		};
	}
	else if (settingName === '_didFillInProviderSettings') {
		return {
			title: '(never)',
			placeholder: '(never)',
		};
	}
	else if (settingName === 'models') {
		return {
			title: '(never)',
			placeholder: '(never)',
		};
	}
	return undefined;
	// throw new Error(`displayInfo: Unknown setting name: "${settingName}"`);

};


const defaultCustomSettings: Record<Exclude<CustomSettingName, 'baseURL'>, undefined> = {
	apiKey: undefined,
	endpoint: undefined,
};


const modelInfoOfDefaultModelNames = (defaultModelNames: { models: string[]; applyModels: string[] }): { models: CodeseekModelInfo[] } => {
	return {
		models: defaultModelNames.models.map((modelName, i) => {
			const isApplyModel = defaultModelNames.applyModels.includes(modelName);
			return {
				modelName,
				isSupportConfig: true,
				isAutodetected: false,
				isApplyModel: isApplyModel,
				isHidden: defaultModelNames.models.length >= 10, // hide all models if there are a ton of them, and make user enable them individually
			};
		})
	};
};

// used when waiting and for a type reference
export const defaultSettingsOfProvider: SettingsOfProvider = {
	[ProviderNames.default]: {
		...defaultCustomSettings,
		...defaultProviderSettings[ProviderNames.default],
		...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.default]),
		_didFillInProviderSettings: true,
		isDefault: true,
	},
	// [ProviderNames.saturn]: {
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.saturn],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.saturn]),
	// 	_didFillInProviderSettings: true,
	// 	isDefault: true,
	// },
	// [ProviderNames.openAI]: {
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.openAI],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.openAI]),
	// 	_didFillInProviderSettings: undefined,
	// 	isDefault: false,
	// },
	// [ProviderNames.deepseek]: {
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.deepseek],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.deepseek]),
	// 	_didFillInProviderSettings: undefined,
	// 	isDefault: false,
	// },
	[ProviderNames.openAICompatible]: { // aggregator
		...defaultCustomSettings,
		...defaultProviderSettings[ProviderNames.openAICompatible],
		...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.openAICompatible]),
		_didFillInProviderSettings: undefined,
		isDefault: false,
	},
	// [ProviderNames.anthropic]: {
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.anthropic],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.anthropic]),
	// 	_didFillInProviderSettings: undefined,
	// 	isDefault: false,
	// },
	// [ProviderNames.gemini]: {
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.gemini],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.gemini]),
	// 	_didFillInProviderSettings: undefined,
	// 	isDefault: false,
	// },
	// [ProviderNames.xAI]: {
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.xAI],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.xAI]),
	// 	_didFillInProviderSettings: undefined,
	// 	isDefault: false,
	// },
	// [ProviderNames.groq]: { // aggregator
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.groq],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.groq]),
	// 	_didFillInProviderSettings: undefined,
	// 	isDefault: false,
	// },
	// [ProviderNames.openRouter]: { // aggregator
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.openRouter],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.openRouter]),
	// 	_didFillInProviderSettings: undefined,
	// 	isDefault: false,
	// },
	// [ProviderNames.ollama]: { // aggregator
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.ollama],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.ollama]),
	// 	_didFillInProviderSettings: undefined,
	// 	isDefault: false,
	// },
	// [ProviderNames.vLLM]: { // aggregator
	// 	...defaultCustomSettings,
	// 	...defaultProviderSettings[ProviderNames.vLLM],
	// 	...modelInfoOfDefaultModelNames(defaultModelsOfProvider[ProviderNames.vLLM]),
	// 	_didFillInProviderSettings: undefined,
	// 	isDefault: false,
	// },
};


export type ModelSelection = { providerName: ProviderNames; modelName: string; isApplyModel: boolean };

export const modelSelectionsEqual = (m1: ModelSelection, m2: ModelSelection) => {
	return m1.modelName === m2.modelName && m1.providerName === m2.providerName;
};

// this is a state
export enum FeatureNames {
	CtrlL = 'Ctrl+L',
	CtrlK = 'Ctrl+K',
	Autocomplete = 'Autocomplete',
	Apply = 'Apply',
}
export type ModelSelectionOfFeature = Record<FeatureNames, ModelSelection | null>;
export type FeatureName = keyof ModelSelectionOfFeature;

export const displayInfoOfFeatureName = (featureName: FeatureName) => {
	// editor:
	if (featureName === FeatureNames.Autocomplete)
		return 'Autocomplete';
	else if (featureName === FeatureNames.CtrlK)
		return 'Quick Edit';
	// sidebar:
	else if (featureName === FeatureNames.CtrlL)
		return 'Chat';
	else if (featureName === FeatureNames.Apply)
		return 'Apply';
	else
		throw new Error(`Feature Name ${featureName} not allowed`);
};


// the models of these can be refreshed (in theory all can, but not all should)
export const refreshableProviderNames: ProviderNames[] = localProviderNames;
export type RefreshableProviderName = typeof refreshableProviderNames[number];




// use this in isFeatuerNameDissbled
export const isProviderNameDisabled = (providerName: ProviderNames, settingsState: CodeseekSettingsState) => {

	const settingsAtProvider = settingsState.settingsOfProvider[providerName];
	const isAutodetected = (refreshableProviderNames as string[]).includes(providerName);

	const isDisabled = settingsAtProvider.models.length === 0;
	if (isDisabled) {
		return isAutodetected ? 'providerNotAutoDetected' : (!settingsAtProvider._didFillInProviderSettings ? 'notFilledIn' : 'addModel');
	}
	return false;
};

export const isFeatureNameDisabled = (featureName: FeatureName, settingsState: CodeseekSettingsState) => {
	// if has a selected provider, check if it's enabled
	const selectedProvider = settingsState.modelSelectionOfFeature[featureName];

	if (selectedProvider) {
		const { providerName } = selectedProvider;
		return isProviderNameDisabled(providerName, settingsState);
	}

	// if there are any models they can turn on, tell them that
	const canTurnOnAModel = !!providerNames.find(providerName => settingsState.settingsOfProvider[providerName].models.filter(m => m.isHidden).length !== 0);
	if (canTurnOnAModel) return 'needToEnableModel';

	// if there are any providers filled in, then they just need to add a model
	const anyFilledIn = !!providerNames.find(providerName => settingsState.settingsOfProvider[providerName]._didFillInProviderSettings);
	if (anyFilledIn) return 'addModel';

	return 'addProvider';
};

export enum CodebaseModes {
	Local = 'Local',
	Remote = 'Remote',
}

export type CodebaseMode = keyof SettingsOfCodebase;

type CommonCodeSettings = {
	process: number | undefined;
	syncedFileNumber: number;
	status: 'indexing' | 'paused' | 'idle' | 'completed';
};

export type SettingsAtCodebase<CodebaseMode extends CodebaseModes> = CommonCodeSettings;

export type SettingsOfCodebase = Record<CodebaseModes, SettingsAtCodebase<CodebaseModes>>;

export type CodebaseSettingsOfRepo = Record<string, SettingsOfCodebase>;

export const defaultSettingsOfCodebase: SettingsOfCodebase = {
	[CodebaseModes.Local]: {
		process: 0,
		syncedFileNumber: 0,
		status: 'idle',
	},
	[CodebaseModes.Remote]: {
		process: 0,
		syncedFileNumber: 0,
		status: 'idle',
	}
};









export type GlobalSettings = {
	autoRefreshModels: boolean;
	aiInstructions: string;
	enableLocalCodeBase: boolean;
	enableRemoteCodeBase: boolean;
	enableCodeBaseConvertFile: boolean;
	autoApprove: boolean;
	httpProxy: string;
	userRules: string;
	clangdCompileCommandsDir: string;
};
export const defaultGlobalSettings: GlobalSettings = {
	autoRefreshModels: true,
	aiInstructions: '',
	enableLocalCodeBase: false,
	enableRemoteCodeBase: false,
	enableCodeBaseConvertFile: false,
	autoApprove: false,
	httpProxy: '',
	userRules: 'Always respond in 中文',
	clangdCompileCommandsDir: '',
};

export type GlobalSettingName = keyof GlobalSettings;
export const globalSettingNames = Object.keys(defaultGlobalSettings) as GlobalSettingName[];


export type ModelOption = { name: string; selection: ModelSelection };

export type ContainerSettings = {
	modelSelections?: ModelSelectionOfFeature;
	chatMode?: ChatMode;
};


