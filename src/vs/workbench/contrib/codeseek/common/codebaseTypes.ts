import { URI } from '../../../../base/common/uri.js';
import { CancellationToken } from '../../../../base/common/cancellation.js';
import { IProjectStructure } from './codeseekFileService.js';
import { FeatureNames } from './codeseekSettingsTypes.js';

export interface ICodebaseService {
	readonly _serviceBrand: undefined;

	search(params: searchParams): Promise<ISearchResult[]>;
}

export type searchParams = {
	userQuery: string;
	repoUri: URI;
	topK?: number;
};

export interface ISearchResult {
	uri: URI;
	range: { startLineNumber: number; startColumn: number; endLineNumber: number; endColumn: number };
	content: string;
}

export type progressParams = {
	repoUri: URI;
	progress: number;
	SyncedFileNumber: number;
}

export type EnsureIndexParams = {
	repoUri: URI;
	projectStructure?: IProjectStructure;
	options?: {
		retryIfLastAttemptFailed: boolean,
		ignoreExisting: boolean,
	};
	onProgress: (p: progressParams) => void;
	useProviderFor?: FeatureNames;
};

export type DeleteRepoIndexParams = {
	repoUri: URI;
};

export type ReindexIfStaleParams = {
	repoUri: URI;
};

export type GetResultsParams = {
	userQuery: string;
	repoUri: URI;
	keywordQuery?: string;
	topK?: number;
};

export type GetLiveResultsParams = {
	userQuery: string;
	keywordQuery: string;
	files: string[];
	token?: CancellationToken;
};

export type UpdateIndexParams = {
	fileUri: URI;
	status?: 'add' | 'update' | 'delete';
};

export type DeleteIndexParams = {
	repoUri: URI;
};

export type PauseRemoteIndexParams = {
	repoUri: URI;
};

export type ResumeRemoteIndexParams = {
	repoUri: URI;
};

export type GetIndexStatusParams = {
	repoUri: URI;
};

// remote codebase
// request params
export interface LLmConfigData {
	provider: string;
	model: string;
	baseUrl: string;
	apiKey: string;
}
export interface ClangdConfigData {
	path: string;
	compileCommandsDir: string;
}

export interface CreateRepoConfig {
	llm: LLmConfigData,
	clangd?: ClangdConfigData
}

export interface CreateRepoParams {
	repoPath: string;
	repoName: string;
	config: CreateRepoConfig;
}

export interface QueryRepoSnapshotParams {
	repoId: string;
}

export interface CreateFileIndexParams {
	repoId: string;
	relativePath: string;
	content: string;
	hashcode: string;
	language: string;
}

export interface DeleteFileIndexParams {
	repoId: string;
	relativePath: string;
}

export interface NoticeRepoIndexCreateCompleteParams {
	repoId: string;
}

export interface QueryContextParams {
	query: string;
	repoId: string;
	topK?: number;
}

// remote codebase response
export interface CodeBaseResponse {
	code: number;
	message: string;
	data: CreateRepoData | RepoSnapshotData | QueryContextData;
}

export interface CreateRepoData {
	repoId: string;
	isComplete: boolean;
	repoName?: string;
	repoPath?: string;
}

export interface RepoSnapshot {
	relativePath: string;
	hashcode: string;
}

export interface RepoSnapshotData {
	repoSnapshot: RepoSnapshot[];
}

export interface QueryContextData {
	contextItems: ContextItem[];
}

export interface ContextItem {
	relativePath: string;
	range: {
		startLineNumber: number;
		startColumnNumber: number;
		endLineNumber: number;
		endColumnNumber: number;
	};
	hashcode: string;
	content: string;
}

export type State = {
	repoId: string;
	repoName: string;
	repoPath: string;
	status: 'running' | 'completed' | 'paused' | 'idle' | 'error';
	projectStructure?: IProjectStructure;
	llm?: LLmConfigData;
	clangd?: ClangdConfigData;
	onProgress?: onProgress;
}

export type EventCodebaseOnProgressParams = Parameters<onProgress>[0];

export type onProgress = (p: progressParams) => void;

export interface IndexStartEvent {
	repoUri: URI
	cancel: () => void
	done: Promise<void>
}

export interface IndexEndEvent {
	repoUri: URI
}
