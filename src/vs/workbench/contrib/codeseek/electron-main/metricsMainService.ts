/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { CancellationToken } from '../../../../base/common/cancellation.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { IEnvironmentMainService } from '../../../../platform/environment/electron-main/environmentMainService.js';
import { IRequestService } from '../../../../platform/request/common/request.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';

import { IMetricsService, METRICS_EVENT_TYPE } from '../common/metricsService.js';


export class CodeseekMetricsMainService extends Disposable implements IMetricsService {
	_serviceBrand: undefined;

	private metricsUrl = "https://wxran.zte.com.cn/ide-monitor/ide/api/telemetry/eventInsert";
	private _initProperties: object = {};

	constructor(
		@IEnvironmentMainService private readonly _envMainService: IEnvironmentMainService,
		@ICodeseekLogger private readonly _logger: ICodeseekLogger,
		@IRequestService protected requestService: IRequestService,
	) {
		super();
		this.initialize(); // 异步初始化
	}

	/**
	 * 服务初始化
	 * 1. 等待应用存储就绪
	 * 2. 获取用户信息和扩展信息
	 * 3. 初始化服务属性
	 */
	private async initialize(): Promise<void> {
		// 初始化服务属性
		this._initProperties = {
			isDevMode: !this._envMainService.isBuilt,
		};
	}

	/**
	 * 捕获并发送一个度量事件
	 */
	capture(event: METRICS_EVENT_TYPE, eventContent: Record<string, any>, userId?: string): void {
		try {
			const traceId = generateUuid();
			process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
			const headers = {
				"accept": "application/json",
				"X-Emp-No": userId ?? '',
				"Content-Type": "application/json"
			};
			const body = JSON.stringify({
				traceId,
				event, eventContent
			});

			this.requestService.request({
				type: 'POST',
				url: this.metricsUrl,
				headers: headers,
				data: body
			}, CancellationToken.None).catch(error => {
				this._logger.error(`[MetricsService] headers: ${JSON.stringify(headers)} , body: ${body}, response: `, error);
			});
		} catch (error) {
			this._logger.error(`[MetricsService] Error capturing metrics event: ${error}`);
		}
	}

	/**
	 * 获取调试属性
	 */
	async getDebuggingProperties(): Promise<object> {
		return this._initProperties;
	}
}
