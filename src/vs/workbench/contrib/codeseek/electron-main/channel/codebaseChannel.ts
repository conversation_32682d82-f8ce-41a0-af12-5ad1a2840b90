import { Emitter, Event } from '../../../../../base/common/event.js';
import { IServerChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { ICodebaseSymfMainService } from '../codebaseSymfMainService.js';
import { CreateRepoConfig, EnsureIndexParams, EventCodebaseOnProgressParams, IndexEndEvent, IndexStartEvent } from '../../common/codebaseTypes.js';
import { ICodebaseRemoteMainService, RemoteEnsureIndexParams } from '../codebaseRemoteMainService.js';

export class CodebaseChannel implements IServerChannel {

	private readonly symfMessageEmitters = {
		onIndexStart: new Emitter<IndexStartEvent>(),
		onIndexEnd: new Emitter<IndexEndEvent>(),
		onProgress: new Emitter<EventCodebaseOnProgressParams>(),
	};

	private readonly remoteMessageEmitters = {
		onIndexStart: new Emitter<IndexStartEvent>(),
		onIndexEnd: new Emitter<IndexEndEvent>(),
		onProgress: new Emitter<EventCodebaseOnProgressParams>(),
	};

	constructor(
		private readonly symfMainService: ICodebaseSymfMainService,
		private readonly remoteMainService: ICodebaseRemoteMainService,
	) { }

	listen(_: unknown, event: string): Event<any> {
		switch (event) {
			case 'onIndexStart_symf':
				return this.symfMessageEmitters.onIndexStart.event;
			case 'onIndexEnd_symf':
				return this.symfMessageEmitters.onIndexEnd.event;
			case 'onProgress_symf':
				return this.symfMessageEmitters.onProgress.event;
			case 'onIndexStart_remote':
				return this.remoteMessageEmitters.onIndexStart.event;
			case 'onIndexEnd_remote':
				return this.remoteMessageEmitters.onIndexEnd.event;
			case 'onProgress_remote':
				return this.remoteMessageEmitters.onProgress.event;
			default:
				throw new Error('Unknown event');
		}
	}

	call(_: unknown, command: string, params: any): any {
		switch (command) {
			case 'ensureSymfIndex':
				return this.symfMainService.ensureIndex(params);
			case 'reSymfindexIfStale':
				return this.symfMainService.reindexIfStale(params);
			case 'getSymfResults':
				return this.symfMainService.getResults(params);
			case 'getSymfLiveResults':
				return this.symfMainService.getLiveResults(params);
			case 'deleteSymfIndex':
				return this.symfMainService.deleteIndex(params);
			case 'getSymfIndexStatus':
				return this.symfMainService.getIndexStatus(params);
			case 'ensureRemoteIndex':
				return this._callEnsureIndex(params);
			case 'deleteRemoteIndex':
				return this.remoteMainService.deleteRemoteIndex(params);
			case 'getRemoteResults':
				return this.remoteMainService.getResults(params);
			case 'startRemoteCodebase':
				return this.remoteMainService.startCodebaseProcess();
			case 'stopRemoteCodebase':
				return this.remoteMainService.stopCodebaseProcess();
			case 'pauseRemoteIndex':
				return this.remoteMainService.pauseRemoteIndex(params);
			case 'resumeRemoteIndex':
				return this.remoteMainService.resumeRemoteIndex(params);
			case 'updateRemoteIndex':
				return this.remoteMainService.updateRemoteIndex(params);
			default:
				throw new Error(`Unknown command: ${command}`);
		}
	}

	private _callEnsureIndex(params: EnsureIndexParams & CreateRepoConfig): Promise<void> {
		if (params.llm.provider === 'default') {
			params.llm.baseUrl = params.llm.model === 'AI-IDE-R1-32B' ? 'http://10.40.25.33:30804/ai-ide-service-1/v1/v1' :
				params.llm.model === 'AI-IDE-FastApply' ? 'http://10.55.58.14:30804/test-service-3/v1/v1' :
					params.llm.baseUrl;
		}

		const params_: RemoteEnsureIndexParams = {
			...params,
			onProgress: (p: EventCodebaseOnProgressParams) => {
				this.remoteMessageEmitters.onProgress.fire(p);
			}
		};
		return this.remoteMainService.ensureIndex(params_);
	}
}
