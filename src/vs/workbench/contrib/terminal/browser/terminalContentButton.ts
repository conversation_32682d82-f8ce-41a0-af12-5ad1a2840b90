/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as dom from '../../../../base/browser/dom.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { DisposableStore, IDisposable } from '../../../../base/common/lifecycle.js';
import { localize } from '../../../../nls.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';
import { IChatThreadService } from '../../codeseek/browser/chatThreadService.js';
import { CODESEEK_OPEN_SIDEBAR_ACTION_ID } from '../../codeseek/browser/sidebarActions.js';
import { ISidebarStateService } from '../../codeseek/browser/sidebarStateService.js';
import { TerminalSelection } from '../../codeseek/common/selectedFileService.js';
import './media/terminalContentButton.css';
import { ITerminalGroupService, ITerminalInstance } from './terminal.js';

// 注册显示终端内容按钮的图标
export const showTerminalContentIcon = registerIcon('terminal-show-content', Codicon.clippy, localize('terminalShowContentIcon', 'Icon for showing terminal content in a notification.'));

// 用于创建并管理"显示终端内容"按钮的类
export class TerminalContentButton implements IDisposable {
	// 静态实例跟踪器，用于在不同组件之间访问
	private static _currentInstance: TerminalContentButton | undefined;

	private readonly _disposables = new DisposableStore();
	private readonly _instanceDisposables = new Map<number, IDisposable>();
	private _container: HTMLElement | undefined;
	private _button: HTMLElement | undefined;
	private _buttonText: HTMLSpanElement | undefined;
	// 添加跟踪当前终端实例的变量
	private _currentInstance: ITerminalInstance | undefined;

	constructor(
		private readonly _parentElement: HTMLElement,
		@INotificationService private readonly _notificationService: INotificationService,
		@ITerminalGroupService private readonly _terminalGroupService: ITerminalGroupService,
		@IChatThreadService private readonly _chatThreadService: IChatThreadService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@ICommandService private readonly _commandService: ICommandService
	) {
		// 将当前实例保存到静态变量中
		TerminalContentButton._currentInstance = this;
		this._createButton();

		// 更新按钮上的快捷键显示
		this._updateKeybindingLabel();

		// 初始时隐藏按钮
		this._hideButton();

		// 监听终端激活实例变化
		this._disposables.add(this._terminalGroupService.onDidChangeActiveInstance(instance => {
			this._handleActiveInstanceChanged(instance);
		}));

		// 监听终端选择内容变化
		this._disposables.add(this._terminalGroupService.onDidFocusInstance(instance => {
			this._handleActiveInstanceChanged(instance);
		}));

		// 初始化时检查当前活动终端
		this._handleActiveInstanceChanged(this._terminalGroupService.activeInstance);
	}

	// 处理活动终端实例变化
	private _handleActiveInstanceChanged(instance: ITerminalInstance | undefined): void {
		// 清除旧实例的事件监听
		if (this._currentInstance && this._currentInstance !== instance) {
			const oldDisposable = this._instanceDisposables.get(this._currentInstance.instanceId);
			if (oldDisposable) {
				oldDisposable.dispose();
				this._instanceDisposables.delete(this._currentInstance.instanceId);
			}
		}

		// 设置新的活动实例
		this._currentInstance = instance;

		if (!instance) {
			this._hideButton();
			return;
		}

		// 添加新实例的选择变化监听
		const disposable = instance.onDidChangeSelection(() => {
			this._updateButtonVisibility(instance);
		});
		this._disposables.add(disposable);
		this._instanceDisposables.set(instance.instanceId, disposable);

		// 立即更新按钮可见性
		this._updateButtonVisibility(instance);
	}

	// 更新按钮可见性
	private _updateButtonVisibility(instance: ITerminalInstance): void {
		if (instance.hasSelection()) {
			this._showButton();
		} else {
			this._hideButton();
		}
	}

	// 显示按钮
	private _showButton(): void {
		if (this._container) {
			this._container.style.display = 'flex';
		}
	}

	// 隐藏按钮
	private _hideButton(): void {
		if (this._container) {
			this._container.style.display = 'none';
		}
	}

	// 静态方法，用于获取当前实例
	public static getCurrentInstance(): TerminalContentButton | undefined {
		return TerminalContentButton._currentInstance;
	}

	// 创建按钮
	private _createButton(): void {
		// 创建一个容器元素
		this._container = document.createElement('div');
		this._container.className = 'terminal-content-button-container';
		this._parentElement.appendChild(this._container);

		// 创建按钮元素
		this._button = document.createElement('div');
		this._button.className = `terminal-content-button codicon ${showTerminalContentIcon.id}`;

		// 创建文字标签元素，并保存引用以便后续更新
		this._buttonText = document.createElement('span');
		this._buttonText.className = 'terminal-content-button-text';
		this._button.appendChild(this._buttonText);

		this._container.appendChild(this._button);

		// 初始化按钮的文本和提示内容
		this._updateKeybindingLabel();

		// 添加点击事件监听器
		this._disposables.add(dom.addDisposableListener(this._button, dom.EventType.CLICK, () => this._showTerminalContent()));
	}

	// 更新按钮上显示的键绑定标签
	private _updateKeybindingLabel(): void {
		if (!this._button || !this._buttonText) {
			return;
		}
		// 更新按钮提示
		this._button.title = localize('terminal.showContent', "将终端选择添加到聊天");
		this._buttonText.textContent = localize('terminal.showContentLabel', "添加终端选择到聊天");
	}

	// 显示终端内容并将其添加到聊天中
	private async _showTerminalContent(instance?: ITerminalInstance): Promise<void> {
		// 获取活动终端实例
		const activeInstance = instance || this._terminalGroupService.activeInstance;
		if (!activeInstance) {
			this._notificationService.info(localize('terminal.noActiveInstance', "没有活动的终端实例"));
			return;
		}

		try {
			// 验证用户是否在终端中选择了内容
			if (!activeInstance.hasSelection()) {
				return;
			}

			// 获取用户选择的内容
			const content = activeInstance.selection || '';
			if (!content || content.trim() === '') {
				return;
			}

			// 创建一个包含终端内容的对象，作为选择项传递给聊天面板
			const terminalSelection: TerminalSelection = {
				type: 'Terminal',
				content: content,
				title: '终端选择内容',
				fileURI: activeInstance.resource,
				selectionStr: content,
				range: null,
				timestamp: Date.now(),
				fromMention: false
			};

			// 检查聊天面板是否已打开，如果没有则打开它
			if (!this._sidebarStateService.isSidebarChatOpen()) {
				await this._commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
			}

			// 将终端内容添加到聊天中
			this._chatThreadService.addSelectionToChat(this._chatThreadService.getCurrentContainerId(), terminalSelection);
		} catch (error) {
			console.error('处理终端内容时出错', error);
			this._notificationService.error(localize('terminal.processingError', "处理终端内容时出错"));
		}
	}

	// 获取实例方法，用于在外部调用
	public async getContentFromInstance(instance: ITerminalInstance): Promise<void> {
		// 直接调用_showTerminalContent方法，只获取用户选择的内容
		return this._showTerminalContent(instance);
	}

	// 更新按钮在页面中的位置
	public layout(): void {
		// 可以在此处添加布局逻辑
	}

	// 资源释放
	public dispose(): void {
		// 如果当前静态实例引用指向此实例，则清除引用
		if (TerminalContentButton._currentInstance === this) {
			TerminalContentButton._currentInstance = undefined;
		}

		// 清理实例特定的可释放对象
		for (const disposable of this._instanceDisposables.values()) {
			disposable.dispose();
		}
		this._instanceDisposables.clear();

		this._disposables.dispose();
		this._container?.remove();
		this._container = undefined;
		this._button = undefined;
		this._buttonText = undefined;
	}
}
