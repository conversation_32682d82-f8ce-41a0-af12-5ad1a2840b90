import { ZteUserInfo } from 'vscode';
import { ExtHostZteUserInfoShape, IMainContext, MainContext, MainThreadZteUserInfoShape } from './extHost.protocol.js';
import { Emitter, Event } from '../../../base/common/event.js';
import { UserInfo } from '../../contrib/codeseek/common/uac/UacloginTypes.js';



export class ExtHostZteUserInfoService implements ExtHostZteUserInfoShape {

	private readonly _proxy: MainThreadZteUserInfoShape;

	private readonly _onDidChangeUserInfo = new Emitter<ZteUserInfo>();
	private readonly _onDidUserLogin = new Emitter<ZteUserInfo>();
	private readonly _onDidUserLogout = new Emitter<void>;

	readonly onDidChangeUserInfo: Event<ZteUserInfo> = this._onDidChangeUserInfo.event;
	readonly onDidUserLogin: Event<ZteUserInfo> = this._onDidUserLogin.event;
	readonly onDidUserLogout: Event<void> = this._onDidUserLogout.event;


	constructor(mainContext: IMainContext) {
		this._proxy = mainContext.getProxy(MainContext.MainThreadZteUserInfo);
	}
	async getZteUserInfo(): Promise<ZteUserInfo | undefined> {
		return await this._proxy.$getZteUserInfo();
	}

	$onDidChangeUserInfo(userInfo: UserInfo) {
		this._onDidChangeUserInfo.fire(userInfo);
	}

	$onDidUserLogin(userInfo: UserInfo) {
		this._onDidUserLogin.fire(userInfo);
	}
	$onDidUserLogout() {
		this._onDidUserLogout.fire();
	}
}
