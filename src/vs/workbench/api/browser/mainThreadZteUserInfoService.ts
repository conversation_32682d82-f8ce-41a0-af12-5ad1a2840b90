import { Disposable } from '../../../base/common/lifecycle.js';
import { ExtHostContext, ExtHostZteUserInfoShape, MainContext, MainThreadZteUserInfoShape } from '../common/extHost.protocol.js';
import { ICodeseekUacLoginService } from '../../contrib/codeseek/common/uac/UacloginTypes.js';
import { extHostNamedCustomer, IExtHostContext } from '../../services/extensions/common/extHostCustomers.js';
import { UserInfo } from '../../contrib/codeseek/common/uac/UacloginTypes.js';

@extHostNamedCustomer(MainContext.MainThreadZteUserInfo)
export class MainThreadZteUserInfoService extends Disposable implements MainThreadZteUserInfoShape {

	private readonly _proxy: ExtHostZteUserInfoShape;

	constructor(
		context: IExtHostContext,
		@ICodeseekUacLoginService private readonly uacLoginService: ICodeseekUacLoginService,
	) {
		super();
		this._proxy = context.getProxy(ExtHostContext.ExtHostZteUserInfo);
		uacLoginService.onDidChangeUserInfo(userInfo => {
			this._proxy.$onDidChangeUserInfo(userInfo);
		})
		uacLoginService.onDidUserLogin(userInfo => {
			this._proxy.$onDidUserLogin(userInfo);
		})
		uacLoginService.onDidUserLogout(() => {
			this._proxy.$onDidUserLogout()
		})
	}
	$getZteUserInfo(): Promise<UserInfo | undefined> {
		return new Promise((resolve, reject) => {
			const userInfo = this.uacLoginService.getUserInfo()
			resolve(userInfo)
		})
	}
}
