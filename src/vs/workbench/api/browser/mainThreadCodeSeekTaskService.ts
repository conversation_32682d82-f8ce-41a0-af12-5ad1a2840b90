import { Disposable, DisposableMap } from '../../../base/common/lifecycle.js';
import { basename } from '../../../base/common/resources.js';
import { URI } from '../../../base/common/uri.js';
import { ICodeEditorService } from '../../../editor/browser/services/codeEditorService.js';
import { Range } from '../../../editor/common/core/range.js';
import { ICommandService } from '../../../platform/commands/common/commands.js';
import { IChatThreadService } from '../../contrib/codeseek/browser/chatThreadService.js';
import { ICodeseekTaskService } from '../../contrib/codeseek/browser/codeseekAgentTaskService.js';
import { getContentInRange } from '../../contrib/codeseek/browser/helpers/readFile.js';
import { CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID, CODESEEK_OPEN_SIDEBAR_ACTION_ID } from '../../contrib/codeseek/browser/sidebarActions.js';
import { ISidebarStateService } from '../../contrib/codeseek/browser/sidebarStateService.js';
import { ChatMode, ICodeseekSettingsService } from '../../contrib/codeseek/common/codeseekSettingsService.js';
// import { IMetricsService } from '../../contrib/codeseek/common/metricsService.js';
import { CodeSelection, FileSelection, StagingSelectionItem } from '../../contrib/codeseek/common/selectedFileService.js';
import { extHostNamedCustomer, IExtHostContext } from '../../services/extensions/common/extHostCustomers.js';
import { ExtHostCodeSeekTaskShape, ExtHostContext, MainContext, MainThreadCodeSeekTaskShape } from '../common/extHost.protocol.js';

export type ExternalTool = {
	toolName: string;
	toolDesc: string;
	needApprove: boolean;
};

export type TaskInfo = {
	taskId?: string;
	externalTools?: ExternalTool[];
};

@extHostNamedCustomer(MainContext.MainThreadCodeSeekTask)
export class MainThreadCodeSeekTaskService extends Disposable implements MainThreadCodeSeekTaskShape {
	private readonly _disposables = new DisposableMap<string>();
	private readonly _toolDisposables = new DisposableMap<string>();
	private readonly _messageDisposable = new DisposableMap<string>();

	private readonly _proxy: ExtHostCodeSeekTaskShape;

	constructor(
		private readonly _extHostContext: IExtHostContext,
		@ICodeseekTaskService private readonly _codeseekTaskService: ICodeseekTaskService,
		@IChatThreadService private readonly _chatThreadService: IChatThreadService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@ICommandService private readonly commandService: ICommandService,
		@ICodeEditorService private readonly _codeEditorService: ICodeEditorService,
		@ICodeseekSettingsService private readonly _codeseekSettingsService: ICodeseekSettingsService,
		// @IMetricsService private readonly _metricsService: IMetricsService,
	) {
		super();
		this._proxy = this._extHostContext.getProxy(ExtHostContext.ExtHostCodeSeekTask);
	}

	$startTask(targetTaskId: string,
		taskType: ChatMode, message: {
			taskDesc: string;
			taskParams: Record<string, any>;
			filePath: string;
			range: Range;
			tools: ExternalTool[];
		}): void {

		const taskParams = message.taskParams || {};
		const selection: StagingSelectionItem = this._getSelection(message.filePath, message.range);

		if (taskType === ChatMode.Ask) {
			this._switchChatMode(taskType);
			this._sendMessageToAsk(message.taskDesc, { ...taskParams, code_snippet: selection.selectionStr }, [selection]);
		}

		if (taskType === ChatMode.Agent) {
			this._switchChatMode(taskType);
			this._sendMessageToAgent(message.taskDesc, { ...taskParams, code_snippet: selection.selectionStr }, [selection], targetTaskId, message.tools);
		}

		const disposable = this._codeseekTaskService.onDidTaskDone(({ taskId }) => {
			if (targetTaskId && taskId && targetTaskId === taskId) {
				this._proxy.$onMessage(taskId, 'success');
			}
		});

		const messageDisposable = this._codeseekTaskService.onDidReceiveMessage(({ taskId, message }) => {
			if (targetTaskId && taskId && targetTaskId === taskId) {
				const content = {
					msgSource: 'agent' as 'agent' | 'ide',
					msgId: `msg-${Date.now()}`,
					...message
				};
				this._proxy.$onMessage(taskId, 'processing', content);
			}
		});

		const toolDisposable = this._codeseekTaskService.onDidToolCall(({ taskId, toolName, toolParam }) => {
			if (targetTaskId && taskId && targetTaskId === taskId) {
				return this._proxy.$callTool(taskId, toolName, toolParam);
			}
		});

		this._disposables.set(targetTaskId, disposable);
		this._toolDisposables.set(targetTaskId, toolDisposable);
		this._messageDisposable.set(targetTaskId, messageDisposable);
	}

	$stopTask(taskId: string): void {
		this._disposables.deleteAndDispose(taskId);
		this._toolDisposables.deleteAndDispose(taskId);
		this._messageDisposable.deleteAndDispose(taskId);
	}

	private _switchChatMode(taskType: ChatMode): void {
		if (this._codeseekSettingsService.state.chatMode !== taskType) {
			this._codeseekSettingsService.setChatMode(taskType);
		}
	}

	private _getSelection(filePath: string, range?: Range): StagingSelectionItem {
		if (filePath) {
			const uri = URI.parse(filePath);
			if (range) {
				const model = this._codeEditorService.getActiveCodeEditor()?.getModel();
				const selectionStr = model ? getContentInRange(model, range) : '';
				const selection: CodeSelection = {
					type: 'Selection',
					fileURI: uri,
					title: basename(uri),
					selectionStr: selectionStr ?? '',
					range: range,
					fromMention: false,
				};
				return selection;
			} else {
				const selection: FileSelection = {
					type: 'File',
					fileURI: uri,
					title: basename(uri),
					selectionStr: null,
					range: null,
					fromMention: false,
					fromActive: false,
					fromEditor: false,
				};
				return selection;
			}
		}

		return {
			type: 'File',
			fileURI: URI.parse(''),
			title: '',
			selectionStr: null,
			range: null,
			fromMention: false,
			fromActive: false,
			fromEditor: false,
		};
	}

	private async _sendMessageToAsk(taskDesc: string, taskParams: { [key: string]: any }, selections: StagingSelectionItem[]) {
		await this._sendMessage(taskDesc, taskParams, ChatMode.Ask, selections);
	}

	private async _sendMessageToAgent(
		taskDesc: string,
		taskParams: { [key: string]: any },
		selections: StagingSelectionItem[],
		taskId: string,
		tools: ExternalTool[]
	) {
		await this._sendMessage(taskDesc, taskParams, ChatMode.Agent, selections, { taskId, externalTools: tools });
	}

	private async _sendMessage(
		taskDesc: string,
		taskParams: { [key: string]: any },
		chatMode: ChatMode,
		selections: StagingSelectionItem[],
		taskInfo?: TaskInfo
	) {
		const containerId = this._chatThreadService.getCurrentContainerId();
		if (this._chatThreadService.isCurrentThreadWorking(containerId)) {
			console.log('chat is working, please wait');
			return;
		}

		if (!this._sidebarStateService.isSidebarChatOpen()) {
			await this.commandService.executeCommand(CODESEEK_OPEN_SIDEBAR_ACTION_ID);
		}
		await this.commandService.executeCommand(CODESEEK_ADD_SELECTION_TO_SIDEBAR_ACTION_ID);
		this._chatThreadService.addUserMessageAndStreamResponse({
			containerId,
			userMessageOpts: { from: 'Chat', userMessage: taskDesc },
			chatMode: chatMode,
			chatSelections: { currSelns: selections },
			agentParamsFromPlugin: taskParams,
			taskInfo: taskInfo
		});
	}

	public override dispose(): void {
		this._disposables.dispose();
		this._toolDisposables.dispose();
		this._messageDisposable.dispose();
		super.dispose();
	}
}
